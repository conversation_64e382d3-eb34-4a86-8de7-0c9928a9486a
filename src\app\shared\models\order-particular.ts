import { Cart } from "./cart.model";
import { Company } from "./company.model";
import { OrderStatus } from "./order";
import { QrCodeData } from "./qr-code";
import { Particular, User } from "./user.models";

export class OrderSupplier {

  _id?: string;
  supplier: Partial<Company>;
  cart: Cart;
  qrCodeData: QrCodeData[];
  user?: Particular;
  status?: OrderStatus;
  appReference?: string;
  validation?: {
    user: Partial<User>;
    date: number;
    raison: string;
  }
  created_at?: number

}

export enum SupplierAction {
  CREATE = 'create_oder_supplier',
  UPDATE = 'update_oder_supplier',
  DELETE = 'delete_oder_supplier',
  VIEW = 'view_oder_supplier',
}