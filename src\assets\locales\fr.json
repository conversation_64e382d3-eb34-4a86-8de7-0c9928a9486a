{"preposition": {"to": "à "}, "button": {"cancel": "Annuler", "download": "Télecharger", "accept": "ACCEPTER", "save-edit-carrier": "Insérer / Modifier Transporteur"}, "locale": {"fr": "fr-FR", "en": "en-Us"}, "reset-password-page": {"title": "Mot de passe oublié ? ", "action-label": "Modifier le mot de passe", "back-link-label": "Retour à la page de connexion", "description": "En renseignant de nouvelles informations, vous écrasez celles précédemment renseignées."}, "signin-page": {"title": "Se connecter", "phone": "Téléphone", "labelOtp": "Entrer votre numero ou votre email", "mdp": "Renseigner mot de passe", "signin-button": "Connexion", "signin-confirm": "Confirmer", "show-password": "Voir mot de passe", "reset-password-link": "Mot de passe oublié ?", "new-account-button": "Nouveau compte", "description": "Renseignez votre numéro de téléphone pour vous connecter.", "text-message": "Saisissez votre numéro de téléphone", "verify-otp": "Saisissez le code qui vient d’être envoyé via votre numéro de téléphone", "change-language": "Changer la langue", "modal-create-account": {"title": "Sélectionnez le type de compte", "next-button-label": "Suivant"}, "modal-otp-description": {"title": "OTP", "next-button-label": "Suivant", "instruction": "Entrez le code de 7 chiffres envoyer à votre média !!!", "label": "Saisissez le code qui vient d’être envoyé via votre numéro de télépne", "button-confirm": "Connexion", "do-not-recieve-the-code": "Vous ne recevez pas votre code ? cliquez sur le lien pour renvoyer", "resend": "<PERSON><PERSON><PERSON>", "show-modal": "J'ai un otp code", "button-login": "CONFIRMER", "resend-otp": " Vous n'avez pas toujours reçu d'OTP ?'", "placeholder": "Saissisez le code temporaire", "change-phone": "Changer de numéro de téléphone"}}, "bottom-sheet-validation": {"validateOparation": "Validation de l'operation", "accountInformation": "Informations du compte", "name": "Nom", "lastName": "Prénom", "tel": "Téléphone", "Quartier": "District", "socialReasean": "Company name", "company": "Compagnie"}, "signup-page": {"first-step": {"description": "Si vous disposez déja d’un compte client chez CIMENCAM, veuillez nous constacter au ", "lastName": "Nom", "firstName": "Prénom", "sponsor": "Nom du promoteur", "sociale name": "Raison social", "phone": "Téléphone", "numberCni": "Numéro de CNI", "numberNui": "Numéro de NIU", "step-label": "Étape"}, "second-step": {"input-profession-label": "Entrer votre profession", "input-profession-placeholder": "Entrer votre profession", "input-serial-number-label": "Entrer votre matricule", "input-serial-number-placeholder": "Entrer votre matricule", "input-social-reason-label": "Raison social", "input-social-reason-placeholder": "Entrer votre Rai<PERSON> social", "input-district-label": "Quartier", "input-password-label": "Mot de passe", "input-confirm-password-label": "Confirmez le mot de passe", "select-company-label": "Sélectionnez une compagnie", "select-direction-label": "Sélectionnez une direction", "select-service-label": "Sélectionnez un service", "select-type-employee": "Type d'employées", "DRH": "DRH", "Cordo": "CORDO-RH", "normal": "Normal", "select-status-employee": "Status of employee", "select-position-label": "Sélectionnez une position", "select-region-label": "Sélectionnez une région", "select-commercialRegion-label": "Région commerciale", "select-city-label": "Sélectionnez une ville", "validate-button-label": "Valider", "cancel-button-label": "Annuler", "back-link-label": "Retour", "retired": "Retraité", "not-retired": "Actif"}}, "profile": {"logout-button-label": "Déconnexion", "save-profile-picture": "Maj Photo de profil", "disable-button-label": "Supprimer mon compte", "title": "Gestion du compte", "retailer": {"edit": "Édition ", "cancel": "Annuler", "input-lastName-label": "Nom", "input-firstName-label": "Prénom", "input-name-label": "Nom", "input-phone-label": "Téléphone", "input-company-label": "Compagnie", "cni": "CNI", "nui": "NUI", "input-social-reason-label": "Raison social", "input-social-reason-placeholder": "Entrer votre Rai<PERSON> social", "select-region-label": "Sélectionnez une région", "select-city-label": "Sélectionnez une ville", "input-district-label": "Quartier", "input-password-label": "Mot de passe", "input-address-label": "<PERSON><PERSON><PERSON>", "tonnageLelft": "Tonnage restant", "profession": "Profession", "update-button-label": "Modifier", "save-button-label": "Enregistrer"}, "client-account": "Compte utilisateur ", "edit-profile": "Editer le profil", "cancel": "Annuler", "account-manage": "Gestion de compte"}, "reporting": {"Reseller-statistics": "Statistiques revendeurs", "title": "Statistiques", "quantities-ordered": "Quantités commandées", "top-products": "Top produits", "Purchasing No.-1": "N°1 des achats :", "Top-suppliers": "Top fournisseurs", "#1-distributors-for-you": "N°1 des distributeurs pour vous", "quantities-purchased": "Quantités achetées", "products": "Produits", "quantity": "Qté", "name": "Nom", "evolution_sale": "Evolution Commandes", "reporting": "Statistiques", "total-sales": "Total des achats", "sales": "<PERSON> ventes", "btn-filter": "Filtre", "bags": "sacs", "avg": "<PERSON><PERSON><PERSON>", "month": "mois", "avg-time-validation": "Temps moyen de validation", "from": "<PERSON>", "to": "au", "clients": "Clients Particuliers associés", "totalClientCount": "(Total des clients)", "clientName": "Nom du client"}, "claim-form-page": {"title": "Réclamation", "description": "Vous pouvez également nous contacter au numéro suivant:", "input-type-label": "caté<PERSON><PERSON> de r<PERSON>", "input-motivation-label": "motif de réclamation", "input-phone-label": "Numéro de téléphone", "input-message-label": "saisir votre message", "submit-button-label": "Envoyer", "add-piece-button-label": "ajoutez une pièce jointe"}, "claims-page": {"title": "<PERSON><PERSON>", "reference": "Référence:", "category": "Catégorie:", "subcategory": "Sous-catégorie:", "status": "Statut:", "date": "Date", "empty-claim": "Vous n'avez aucune ré<PERSON>", "add-claim": "Faire une Nouvelle Réclamation"}, "claim-page": {"tabs": {"in-progres": "En Cours", "treat": "Traitées"}}, "claims-page-detail": {"title": "Détails de la Réclamation"}, "feedback-detail": {"message": "Message:", "download-attachment": "Télécharger la Pièce Jointe"}, "order-new-page": {"information-label": "Informations de votre commande", "searchbar-label": "Rechercher produit", "new-order": "Nouvelle commande", "maintenance": {"first": "L'application Click Cadyst est actuellement en maintenance.", "second": "Revenez dans quelques instants", "third": "Merci pour votre compréhension."}, "first-step": {"capacityPerYear": "Capacité de tonnage annuelle restante", "capacityLeft": "Capacité de tonnage restante", "title": "Sélectionner le point d'enlèvement", "select-packaging-label": "Ensachage", "select-distributor-label": "Choisir un distributeur", "select-city-label": "Choisir la ville", "select-company": "Sélectionnez un client", "input-location-label": "<PERSON><PERSON> le lieu précis", "delivery-date": "Date de livraison souh<PERSON>", "input-phone-label": "Sai<PERSON> le numéro de téléphone", "input-phone-driver": "Numero de téléphone du chauffeur", "input-deliver-address-label": "Liv<PERSON>son à mes adresses", "select-address-label": " <PERSON><PERSON> une adresse ", "next-button-label": "Suivant", "question-about-delivery": {"label": "Voulez vous être livré?", "yes-button": "O<PERSON>", "no-button": "Non"}, "select-region": "Choisir une region", "select-city": " Choisir une ville", "choose-store": "Choissisez votre <PERSON>", "choose": "CHOISIR", "supplier": "Autres distributeurs", "delivery-location": "<PERSON><PERSON><PERSON> de livraison prévue", "destination-address": "<PERSON><PERSON><PERSON>", "driver-info": "Informations du transporteur", "driver-name": "Nom du chauffeur", "driver-id": "CNI du chauffeur", "driver-vehicle": "Plaque d'immatriculation", "driver-category": "<PERSON><PERSON><PERSON><PERSON> de camion", "driver-license": "Permis de conduire", "category": "Autre categorie"}, "schedule-list": {"title": "Mes planifications", "heure": "<PERSON><PERSON>", "camions": "Camions", "quantity": "Quantité", "new-line": "Nouvelle ligne", "add-line": "Ajouter Quantité", "empty-list": "Vous n'avez encore aucune palnification"}, "second-step": {"next-button-label": "SUIVANT", "title": "Cliquez sur un produit", "title-particular": "Je choisis mes sacs", "quantity": "Quantité", "amount": "Coût", "all": "Tous", "bag-off": "Sacs", "empty-offer-price": "Vous n'avez aucune offre de prix"}, "third-step": {"title": "Récapitulatif des achats", "title-shipping": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carrier-title": "Informations du Transporteur", "amount": "<PERSON><PERSON>", "amount-ht": "Montant HT", "taxes": "Taxes", "cart-amount": "<PERSON><PERSON> panier", "discountCash": "Comptant", "shipping-costs": "Frais de livraison", "total-ttc": "TOTAL TTC", "date-of-delivery": "Date de livraison", "prepayment": "Précompte", "purchased-product": "Produits achetés", "swipe-to-show-element": "Glisser vers la droite pour voir la suite", "payment-label": "Sélectionner le mode de paiement ", "next-button-label": "COMMANDER", "modify-button-label": "MODIFIER", "nbBag": "Nb sacs", "unitCost": "Coût unitaire", "unitPrice": "Prix unitaire", "totalPrice": "Prix Total", "seeMore": "Voir plus", "qte": "Qté", "total-tons": "Tonnage total", "product": "Produit(s)", "flour": "FARINES", "parcel": "Sacs", "deliveryDiscount": "<PERSON><PERSON> livraison", "apply-points": "APPLIQUER MES POINTS"}, "planning-modal": {"title": "Commande", "title-quantity": "Quantité de sac", "remainning-quantity": "Quantités disponible en stock", "tons": "<PERSON><PERSON>(s)", "quantity": "Quantités à acheter", "label-quantity": "<PERSON><PERSON>", "quick-select": "Choix rapide", "date": "Date d’enlèvement", "quart-time": "Choisir un quart temps", "hour": "Heure d’enlèvement", "select-schedule": "Type de planification", "indication-quantity": "<PERSON><PERSON><PERSON> les quantités à enlever", "remains": "Il vous reste {{value1}} / {{value2}} tonnes", "truck": "Nombre de Camions", "tonne-truck": "Tonnage/Camions", "nb-tone": "Nombre de Tonne", "form-quantity": "Planifications par quantités", "form-date": "Planifications par date", "schedule-button-label": "Voir mes Planifications", "save-button-label": "Enregistrer"}, "last-step": {"back-button-label": "Retour à l'accueil", "thanks": "CLIC CADYST VOUS REMERCIE", "for-your-orders": "pour votre commande"}}, "order": {"detail": {"title": "Détail Commande", "reference": "N°", "name": "Nom", "email": "Email", "created-at": "Date de création", "validated-at": "Date validation", "product-list": "Liste des produits", "product-info-slide": "Glisser vers la droite pour voir la suite", "order-recap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> commande", "motif-rejet": "<PERSON><PERSON><PERSON>", "payment-mode": "Mode de paiement", "transaction-ref": "transaction Id", "total-ht": "Total HT", "total-ttc": "Total TTC", "delivery": {"title": "Information de livraison", "mode": "Mode", "location": "<PERSON><PERSON>", "amount": "<PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>"}}, "update": {"title": "<PERSON>se à jour de la commande"}, "valid": {"title": "Validation de la commande", "desc": "Vous êtes sur le point de valider cette commande, confirmer votre action en renseignant une reference"}, "rejection": {"description": "Vous êtes sur le point de rejeter cette commande. Veuillez saisir un motif détaillé pour cette action.", "reasonLabel": "<PERSON><PERSON><PERSON>", "placeholder": "Entrez ici le motif du rejet..."}}, "update-order": {"title": "Produit", "packaging": "Packaging", "quantity": "Quantité en sac(s)", "unit-price": "Prix unitaire", "total-price": "Prix Total"}, "bottom-sheet": {"other": {"title": "Autres", "add-referral": "Ren<PERSON><PERSON><PERSON> fi<PERSON>l"}, "payment": {"label": "Confirmez-vous le paiement de votre commande d'un montant de", "mode": "Mode de paiement", "reference": "Réference commande (falcutatif)", "walletNber": "Numéro wallet", "otpInsert": "Inserer otp", "account-ref": "Numéros de téléphone", "limitation": "Votre commande ne peut excéder le montant de", "specification": "Le paiement de votre commande se fera via", "plateform": "sur cette plateforme", "enter-otp": "Entrer le code otp", "ref-check": "Entrer le code du chéque", "num-transaction": "Numéros du bordereaux", "amount-num": "Montants de la transaction", "send-otp": "Envoyer le code otp", "send-wallet": "Vérifier le wallet", "select-bank": "Selectionner une banque", "validate-button-label": "VALIDER LE PAIEMENT", "send-validate-button-label": "COMMANDER", "amount-per-month": "Montant par mois", "monthlyPayment": "Nombre de mensualités", "startDatePaiement": "Date de début du paiement", "send-in-validation-btn": "Commander", "error-balance-insufficient-amount": "Le solde de votre compte est insuffisant pour effectuer cette commande, vous avez la possibilité de l'envoyer en validation", "visa-label": "<PERSON><PERSON> <PERSON><PERSON> renseigner les informations de votre carte.", "visa-name": "Nom sur la carte", "visa-mouth": "Mois d'expiration", "visa-Year": "Année d'expiration", "visa-cart-number": "Numéro de la carte", "visa-security-code": "Code de sécurité"}}, "popover": {"product-detail": {"title": "Autres", "unit-price": "Prix Unitaire", "quantity": "Quantité", "amount": "Coût"}}, "tab-bar": {"scan": "<PERSON><PERSON>", "indirect-command": "Clients", "points": "Points", "account": "<PERSON><PERSON><PERSON>", "purchases": "Achats", "home": "Accueil", "marketPlace": "Boutique", "order": "Historique", "order-retail": "Cmd rev", "other": "Autres", "orderHistory": "Historique", "reward-products": {"title": "Produits de récompense", "available": "Disponible dans", "countdown": {"days": "jours", "hours": "heures", "minutes": "minutes", "seconds": "secondes"}, "deadline": "Date limite", "gifts": "<PERSON><PERSON>", "point": "points", "empty": "Aucun produit de récompense disponible", "countdown-ended": "Le compte à rebours est terminé ! Vous pouvez maintenant réclamer vos récompenses."}}, "home-page-particular": {"title": "Programme de fidé<PERSON>é <PERSON>", "description": "Gagnez de nombreux lots en achetant votre farine dans votre application mobile", "button": "Débutez l'achat", "product-description": "AMIGO Votre unique compagnons pour la réalisation de vos beignets d’exception", "video-unvailable": "Vidéo non disponible sur votre navigateur", "bag": "sac", "welcome": "Accueil", "formats": "Formats amigo"}, "home-page": {"title": "Bienvenue", "country": "<PERSON><PERSON><PERSON>", "product-title": "<PERSON><PERSON><PERSON> ventes", "product-sugestions": "Sugestions", "product-tendency": "Tendances", "offer-title": "Offres Spéciales", "see-all-button": "Voir tout", "see-offer-button": "VOIR L’OFFRE ", "pub": {"cimencam": {"description": "<PERSON><PERSON><PERSON> prêt à l’emploi", "do-order-button-label": "Commander"}, "binastore": {"description": "<PERSON><PERSON><PERSON><PERSON>", "know-more-button": "EN SAVOIR +"}}, "update-text": "Pour bénéficier des dernières fonctionnalités et des mises à jour de sécurité, veuillez cliquer sur le bouton ci-dessous.", "uptodate": "METTRE à JOUR", "update-cancel": "ANNULER"}, "market-place": {"specical-section": "Spécial mama beignets", "see-more-button": "Voir plus", "tendency": "Tendances", "other-products": "Autres produits", "txt-made": "<PERSON><PERSON><PERSON><PERSON><PERSON> au Cameroun", "pay": "PAYER", "reclaim": "RECLAMER", "fill-quantity": "Renseigner les quantités"}, "redirect-sabitou-modal": {"text": "Le guide adapté au contexte camerounais, pour professionnels et non professionnels,recommandé par l'ONIGC et l'ONAC."}, "history-page": {"title": "<PERSON><PERSON> commandes", "purchase": "Historique des achats", "startDate": "Date de début", "endDate": "Date de fin", "title-filter": "Filtrer par", "btn-filter": "<PERSON><PERSON><PERSON>", "btn-reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref": "Reference", "qty": "Quantité", "status": "Statut", "tabs": {"in-progres": "En attente", "credit": "À credit", "prevalidate": "Pré-validée", "validate": "Validées", "Rejected": "Renvoyées"}, "reference": "N°", "distributor-name": "Distributeur", "amount": "<PERSON><PERSON>", "empty-order": "Vous n'avez aucune commande", "employee-name": "Nom", "update-nber": "Nombre de modifications", "status-cancelled": "Statut d'annulation", "cancellation": "Demande d'annulation", "client-name": "Client", "particular": "<PERSON><PERSON>"}, "all-orders-page": {"title": "Commandes", "my-orders": "<PERSON><PERSON> commandes", "employees-orders": "Commandes employées à crédit"}, "retrievement-page": {"title": "Bons de livraison", "startDate": "Date de début", "endDate": "Date de fin", "title-filter": "Filtrer par", "btn-filter": "<PERSON><PERSON><PERSON>", "uploadText": "<PERSON><PERSON><PERSON> une photo", "btn-reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref": "Reference", "tabs": {"in-progres": "En attente", "validate": "Validées"}, "reference": "N° B.L", "jde": "N° JDE", "ae": "N° AE", "qtyReceved": "<PERSON><PERSON> reçue", "qtyOrdered": "Qté commandée", "qtyShipped": "Qté liv<PERSON>e", "product": "Produit", "driverName": "Conducteur", "back": "Retour", "save": "Enregistrer", "primaryVehicleId": "Immatriculation", "Qty-recieved": "Insérer la quantité reçue (tonne)", "imgLabel": "Insérer une image de B.L ", "empty-order": "Vous n'avez aucune commande", "editRef": "Edition du B.L", "addElts": "Renseigner les quantités livrées"}, "refresher": {"refreshing": "Actualisation", "pull": "Tirez pour actualiser"}, "reseller-new-page": {"bags": "sacs", "first-step": {"select-region-label": "Sélectionner une région", "select-city-label": "Sélectionner une ville", "select-distributors-label": "Sélectionner un Distributeur", "select-packaging-label": "Sélectionner un packaging", "next-button-label": "Suivant", "enter-qdty": " Entrer la quantité", "qdty-bag": "Quantité en sac", "qdty-tons": "Quantité en tonne (T)", "cancel": "Annuler", "valid": "Valider", "qdty-sheep": "<PERSON>uant<PERSON> retire"}, "second-step": {"title": "Cliquer sur un produit", "next-button-label": "Suivant"}, "third-step": {"title": "Récapitulatif des achats", "region": "Region", "city": "Ville", "distributors": "Distributeur", "packaging": "Packaging", "product-order": "Produit commandé", "confirm the order": "<PERSON><PERSON><PERSON><PERSON>", "label": "Saisissez votre référence. (optionnel)"}, "history-page": {"title": "<PERSON><PERSON> commandes", "title-reseller": "Commandes revendeurs", "startDate": "Date de début", "total-quantity": "Quantités total", "endDate": "Date de fin", "title-filter": "Filtrer par", "btn-filter": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "seller": "<PERSON><PERSON><PERSON>", "points-to-validate": "points a validée", "points": "Points", "btn-reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref": "Reference", "tabs": {"in-progres": "En attentes", "validate": "Validées", "prevalidate": "Pré-validées", "rejected": "Renvoyées", "Waiting": "Attente validation "}, "reference": "N°", "distributor-name": "Distributeur", "empty-order": "Vous n'avez aucune commande"}, "retrievement-page": {"title": "Bons de livraison", "startDate": "Date de début", "total-quantity": "Quantités total", "endDate": "Date de fin", "recap": "<PERSON><PERSON><PERSON> du bon ", "title-filter": "Filtrer par", "btn-filter": "<PERSON><PERSON><PERSON>", "btn-reset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref": "Reference", "reference": "N° B.L", "jde": "N° JDE", "ae": "N° AE", "qtyReceved": "<PERSON><PERSON> reçue", "qtyOrdered": "Qté commandée", "qtyShipped": "Quantités livrées", "product": "Produit", "driverName": "Conducteur", "back": "Retour", "save": "Enregistrer", "primaryVehicleId": "Immatriculation", "add-Qtyrecieved": "Veuillez inserer la quantité reçue", "adds": "Renseigner les quantités livrées", "editRef": "Edition du Bon de Livraison", "imgLabel": "Insérer une image de B.L ", "empty-order": "Vous n'avez aucun bon de livraison"}, "detail": {"title": "Détail Commande", "reference": "N°", "created-at": "Date de création", "total-quantity": "Quantités totales", "product-info-slide": "Glisser vers la droite pour voir la suite", "order-recap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> commande", "region": "Region", "city": "Ville", "distributors": "Distributeur", "packaging": "Packaging", "point-unvalidate": "Point non Validées", "point-validate": "Point validées", "bags": "sacs", "congrat": "Votre commande a été envoyée en validation. ", "produit": "Produit commande", "quantity": "Qté", "quantity-ship": "Qté retiré", "para": "Cliquez sur les produits pour modifier les quantités", "modify": "Modifier", "image": "Ajouter une image", "piece-join": "Parcourez et choisissez les fichiers que vous souhaitez télécharger depuis votre appareil.", "upload-image": " Prendre une image", "valide": "Valider", "rejete": "<PERSON><PERSON><PERSON> ", "UPLOAD-IMAGE": "CHARGER UN DOCUMENT", "VIEW-IMAGE": " VOIR LES PHOTOS", "valid-order": "VALIDER LA COMMANDE", "download-order": "TÉLECHARGER LE BON DE COMMANDE"}}, "account-balance-page": {"title": "Mon compte", "description-balance": "Mis à jour le", "at": "à", "creditLimit": "Plafond de crédit", "payment": "Paiements/Encaissements", "amountAvailable": "Ensemble des enlèvements déjà facturés mais non payés.", "availableAmount": "Solde disponible", "bill-fail": "Facture(s) échue(s)", "creditReturned": "Avoir client", "outstanding-bill": "Solde comptable", "others-bill": "Autres notes de débit", "open-order": "Commandes ouvertes", "invoicedAmount": "Commandes  facturées", "openorderAmount": "Commandes non facturées", "histogram": "Histogramme", "limit-credit-desc": "Montant de crédit maximal a utiliser", "bill-fail-desc": "Factures dont le délai de paiement est dépassé.", "open-order-desc": "Commandes non enlevées ou enlevées mais non facturées", "others-bill-desc": "Autres charges facturées"}, "companie-account-page": {"title": "Liste des compagnies", "btn-filter": "<PERSON><PERSON><PERSON>", "empty-company": "Aucune compagnie trouvée", "filter": {"title": "Filtre", "name-label": "Nom", "phone-label": "N° Téléphone", "region-label": "Région commerciale", "btn-submit": "Appliquer"}}, "company-detail-page": {"title": "Détail ", "tabs-menu": {"information": "Informations", "account": "<PERSON><PERSON><PERSON>", "address": "Adresses", "balance": "Solde"}, "empty-address": "<PERSON><PERSON><PERSON> adresse trouvé", "empty-account": "Aucun compte trouvé", "information": {"name-label": "Raison sociale", "status-label": "Status fiscal", "niu-label": "Numéro de contribuable", "rccm-label": "Régistre de commerce", "phone-label": "Téléphone", "commercial-label": "Commercial Associé", "regionCom-label": "Région Commercial", "region-label": "Région", "city-label": "Ville", "district-label": "Quartier"}, "account": {"name-field": "Nom :", "phone-field": "N° Téléphone :", "email-field": "Email :"}, "address": {"description": "Description :", "usine": "Usine :", "shipTo": "ShipTo ID :"}, "balance": {"title": "Solde du compte au", "text": "Mis à jour le", "limit-credit": "<PERSON>ite <PERSON>", "payment": "Paiements/Encaissements", "credit-returned": "Avoir Client", "invoiced-amount": "Commandes facturées", "open-order-amount": "Commandes non facturées"}}, "reconnection": "Veuillez vous reconnecter", "reprint-invoice-page": {"startDate": "Date de début", "endDate": "Date de fin", "tel": "Téléphone", "title-filter": "Edition de factures", "btn-filter": "Editer", "btn-reset": "Retour"}, "jde-report-page": {"startDate": "Date de début", "endDate": "Date de fin", "title-filter": "Extrait de compte", "btn-filter": "Editer", "btn-reset": "Retour"}, "jde-loads-not-dispatched-page": {"startDate": "Date de début", "endDate": "Date de fin", "title-filter": "AES non chargées", "btn-filter": "Editer", "btn-reset": "Retour"}, "reload-balance-page": {"title": "<PERSON><PERSON><PERSON> <PERSON>", "balance": "SOLDE DISPONIBLE", "account-history": "Historique des Recharges", "no-data-found": " Aucune recharge trouvé", "make-recharge": "EFFECTUER UNE RECHARGE"}, "reload-balance-details": {"details": "Detail de la Recharge", "company-name": "Nom de la Compagnie", "user-name": "Nom de l'utilisateur", "transaction-id": "identifiant Transaction", "amount": " <PERSON><PERSON>", "payment-mode": "Mode de paiement", "status": "statut", "transaction-nbr": "Numero de transaction", "refill-date": "Date de recharge", "close-btn": "FERMER"}, "notification": {"your-notification": "Mes notifications", "notification-not-found": "Pas de notification", "selected": "Sélectionnée(s)"}, "removals": {"title": "Enlèvements", "removal-name": "Nom", "removal-status": " Statut", "removal-date": "Date"}, "faq-page": {"title": "Foire aux questions", "empty-faq": "Aucune question trouvée"}, "contact-page": {"title": "Contacts", "call": "<PERSON><PERSON>", "mail": "Email", "website": "Site web", "message": "Messagerie-WhatsApp", "address": "<PERSON><PERSON><PERSON>"}, "item-details-page": {"product-information": "Informations produits", "applications": "Applications", "benefits": "Bénéfices", "features": "Fonctionnalités", "others": "Autres", "description": "Description", "reviews": "<PERSON><PERSON>", "specifications": "Spécifications", "camerounaise-description": "Farine de gamme standard réservée aux experts boulangers et pâtissiers. Sa pâte flexible, malléable et légère offre des opportunités pour différentes applications dont seuls les artistes ont le secret. Nécessite plus de technique et équipement associé.", "colombe-description": "<PERSON><PERSON> de gamme moyenne, elle est le rêve qui nourrit l'envie du métier de boulanger de par son niveau de sécurité dans la production. Sa force boulangère et son aptitude à résister aux différentes imperfections de la production font d'elle l'ami incontesté et infaillible des boulangers. Tolère technicité et équipement approximatif.", "pelican-description": "Farine haut de gamme, c'est une farine spéciale et forte où toutes recettes boulangères et pâtissières sont possibles. Elle présente un large spectre de tolérance et débouche sur des produits de qualité supérieure. Tolère technicité et équipement approximatif.", "amigo-description": "<PERSON>ine spéciale beignet réservée aux différentes applications beignets. La légèreté de sa pâte et son aptitude à donner beaucoup de beignets en font un atout pour les utilisateurs.", "default-description": "Description non disponible pour ce produit.", "amigo-applications": "Parfait pour la fabrication de divers types de beignets, fritures et autres pâtisseries frites.", "amigo-benefits": "Produit des beignets légers et moelleux avec un excellent contrôle de l'absorption d'huile.", "amigo-features": "Mélange spécial de farines pour une texture et un goût optimaux dans les produits frits.", "camerounaise-applications": "Idéal pour la fabrication artisanale du pain, des pâtisseries et des produits de boulangerie fine.", "camerounaise-benefits": "Produit du pain avec une excellente structure de croûte et de mie.", "camerounaise-features": "Teneur élevée en protéines pour un fort développement du gluten.", "colombe-applications": "Convient à une large gamme de produits de boulangerie, du pain aux pâtisseries simples.", "colombe-benefits": "Résultats constants même avec des conditions de production variables.", "colombe-features": "Teneur en protéines équilibrée pour une bonne stabilité de la pâte.", "pelican-applications": "Parfait pour les produits de boulangerie et de pâtisserie haut de gamme, y compris les recettes complexes.", "pelican-benefits": "Produit des produits de boulangerie de qualité supérieure avec un excellent goût et une texture exceptionnelle.", "pelican-features": "Mélange premium de farines avec une force et une stabilité exceptionnelles.", "default-applications": "Applications non disponibles pour ce produit.", "default-benefits": "Avantages non disponibles pour ce produit.", "default-features": "Caractéristiques non disponibles pour ce produit."}, "language": {"current": "(<PERSON>ue actuelle)"}, "months": {"1": "janvier", "2": "<PERSON><PERSON><PERSON><PERSON>", "3": "mars", "4": "avril", "5": "mai", "6": "juin", "7": "juillet", "8": "août", "9": "septembre", "10": "octobre", "11": "novembre", "12": "décembre"}, "account-management": {"title": "Gestion des comptes", "manager": "Gestionnaire", "status": "Actif", "other": "Autres comptes", "text": "Cliquez sur les flèches pour changer de compte", "title-account": "Choisir le compte d'authentification", "text-account": "Vous êtes détenteur de plusieurs compte, bien vouloir sélectionner un compte", "btn": "<PERSON><PERSON><PERSON>"}, "fidelity-page": {"title": "Programme de fidelité", "points": "Points validées", "add-referral": "<PERSON><PERSON><PERSON>", "pts": "Pts", "waiting": "En attente de validation", "my-points": "Mes points", "available-Gifts": "Cadeaux disponibles", "advantages-colombe": "Advantages colombe", "advantages-title": "Avantages", "description-title": "Description", "category": "Autres Catégories du programme", "no-gifts": "Pas de cadeaux disponibles", "referral-list": "Liste des filleuls", "sponsor-title": "<PERSON><PERSON>s par<PERSON>age", "sponsor-desc": "<PERSON><PERSON> avez é<PERSON> par<PERSON>", "input-referral": "Renseigner le numéro de téléphone du fieul", "see-more": "En savoir plus le parrainage", "referal": "PARRAINER", "inactive-qr-code": "QR Code inactif", "isCheckingQrCode": "Verification scan..."}, "indirect-user": {"title": "Clients", "phone": "N° Téléphone", "address": "<PERSON><PERSON><PERSON>"}, "list-user": {"title": "Listes des utilisateurs", "direct": "<PERSON><PERSON><PERSON> directs", "indirect": "Clients indirects"}, "user-info": {"title": "Informations sur l'utilisateur", "full-name": "Nom complet:", "phone": "Téléphone:", "region": "Région:", "location": "Localisation:", "category": "Catégorie:", "created-at": "<PERSON><PERSON><PERSON>:", "enabled": "Activé:", "company": "Compagnie :", "particular": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>", "yes": "O<PERSON>", "no": "Non"}, "indirect-clients": {"title": "Clients", "name": "Nom + Prénom", "business-name": "Appellation commerciale", "phone": "Numéro de Téléphone", "location": "Entrez une adresse précisee", "vendors": "<PERSON><PERSON> - grossiste", "images": "Images", "order-new-page.planning-modal.save-button-label": "Enregistrer", "button.cancel": "Annuler", "reseller-new-page.first-step.select-region-label": "Sélectionner une région", "city": "Sélectionnez une ville", "ville": "Ville", "Neighborhood": "Quartier", "enter-net": "Saisis<PERSON>z le quartier", "Type-client": "Type de client indirect", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "É<PERSON>er", "save": "Enregistrer", "empty": "Aucun utilisateur trouvé", "localisation": "Localisation", "locations": "Mes localisations", "reset": "Renitialiser", "add": "<PERSON><PERSON><PERSON>"}, "list-order": {"title": "Listes des commandes", "direct": "Commandes directes", "indirect": "Commandes indirectes"}, "status": {"pending": "En attente", "approved": "Traité", "accepted": "Accepté", "rejected": "<PERSON><PERSON><PERSON>", "refused": "<PERSON><PERSON><PERSON><PERSON>"}, "feedback": {"delivery": "<PERSON><PERSON><PERSON>", "billing": "Facturation"}, "general-condtion": {"title": "CONDITIONS GENERALES DE VENTE APPLICABLES AUX PRODUITS VENDUS PAR LA SOCIETE LA Cadyst Grain S.A.", "sub-title1": "1- DISPOSITION GENERALE", "text1": "Les présentes Conditions Générales de Vente (ci-après « CGV ») constituent la base unique de la négociation commerciale et s’appliquent pleinement, à toute commande de produits (ci-après les « Produits ») effectuée par un client de LA Cadyst Grain S.A (ci-après le « Client ») auprès de LA Cadyst Grain S.A (ci-après individuellement la « Partie » et collectivement les « Parties »), sauf accord exprès de LA Cadyst Grain S.A sur tout ou partie des conditions générales d’achat du Client. Toute condition proposée par le Client sera donc, à défaut d’acceptation expresse, inopposable à LA Cadyst Grain S.A, quel que soit le moment où elle aura pu être portée à sa connaissance. En conséquence, toute Commande (tel que définie ci-après) réalisée auprès de LA Cadyst Grain S.A emporte acceptation sans réserve des présentes CGV et du tarif de LA Cadyst Grain S.A en vigueur.", "text11": "Les présentes CGV annulent et remplacent toutes les conditions générales de vente antérieures qui auraient existé entre les Parties. Elles sont susceptibles d’être modifiées à tout moment par LA Cadyst Grain S.A en cas notamment de modification significative du contexte normatif ou socioéconomique. Dans une telle hypothèse, LA Cadyst Grain S.A transmettra les nouvelles CGV au Client dans un délai raisonnable.", "sub-title2": "2- COMMANDES"}, "recap-scan": {"validate": "Valider", "supplier": "Distributeur", "title": "Récapitulatif des coupons"}, "method-order": {"title": "Valider vos points"}, "qr-orders": {"select-client": "Selectionner un client", "text": "Vous êtes sur le point de scanner et de valider automatiquement les points du client avec les informations:", "qr": "QR Code", "user-info": "INFORMATIONS DE L'UTILISATEUR", "scan": "Scanner votre coupon d'achat", "title": "Sacs achetés", "scan-text": "Scannez vos coupons d'achats et obtenez des points de fidélité", "text-button": "Scannez votre coupon"}}