import { User } from '../models/user.models';
import { Pipe, PipeTransform } from '@angular/core';
import { CancellationStatus, OrderStatus, OrderStatusSupplier } from '../models/order';

@Pipe({
  name: 'statusOrderRetail',
})
export class StatusOrderRetailPipe implements PipeTransform {
  transform(value: number, ...args: unknown[]): string {
    if (value === OrderStatusSupplier.CREATED) {
      return 'crée';
    }
    if (value === OrderStatusSupplier.PREVALIDATED) {
      return 'prevalider';
    }
    if (value === OrderStatusSupplier.REJECTED) {
      return 'réjeter';
    }
    if (value === OrderStatusSupplier.VALIDATED) {
      return 'valider';
    }
    return '';
  }
}

@Pipe({
  name: 'colorStatusOrderRetail',
})
export class ColorStatusOrderRetailPipe implements PipeTransform {
  transform(value: number, ...args: unknown[]): string {
    if (value === OrderStatusSupplier.CREATED) {
      return 'bg-info-100';
    }

    if (value === OrderStatusSupplier.REJECTED) {
      return 'bg-danger-100';
    }
    if (value === OrderStatusSupplier.VALIDATED) {
      return 'bg-primary-300';
    }
    return '';
  }
}

@Pipe({
  name: 'statusOrder',
})
export class StatusOrderPipe implements PipeTransform {
  transform(value: number, ...users: unknown[]): string {
    if (value === OrderStatus.CREDIT_IN_VALIDATION) {
      return `En Attente DRH`;
    }
    if (value === OrderStatus.CREDIT_IN_AWAIT_VALIDATION) {
      return `En Attente Commercial`;
    }
    if (value === OrderStatus.PAID) {
      return `En Attente`;
    }
    if (value === OrderStatus.CREATED) {
      return `En Attente`;
    }
    // if (value === OrderStatus.CREDIT_REJECTED) {
    //   return `Renvoyer`;
    // }
    if (value === OrderStatus.REJECTED) {
      return 'Rejetée';
    }
    if (value === OrderStatus.VALIDATED) {
      return `validé`;
    }
    return '';
  }

}

@Pipe({
  name: 'colorStatusOrder',
})
export class ColorStatusOrderPipe implements PipeTransform {
  transform(value: number, ...args: unknown[]): string {
    if (value === OrderStatus.CREATED) {
      return 'bg-info-100 clr-info-500';
    }
    if (value === OrderStatus.PAID) {
      return 'bg-info-100 clr-info-500';
    }
    if (value === OrderStatus.CREDIT_IN_VALIDATION) {
      return 'bg-info-500 clr-default-400';
    }
    if (value === OrderStatus.CREDIT_IN_AWAIT_VALIDATION) {
      return 'bg-info-500 clr-default-400';
    }
    if (value === OrderStatus.CREDIT_REJECTED) {
      return 'bg-danger-100 clr-danger-400';
    }
     if (value === OrderStatus.CREDIT_REJECTED) {
      return 'bg-danger-100 clr-danger-400';
    }
    if (value === OrderStatus.REJECTED) {
      return 'bg-danger-100 clr-danger-400';
    }
    if (value === OrderStatus.VALIDATED) {
      return 'bg-success-200 clr-primary-400';
    }
    return '';
  }
}


@Pipe({
  name: 'colorStatusCancelled',
})
export class ColorStatusCancelledPipe implements PipeTransform {
  transform(value: number): string {
    if (value === CancellationStatus.ISSUE) {
      return 'bg-tertiary-200';
    }

    if (value === CancellationStatus.REFUSED) {
      return 'bg-danger-100 clr-default-400';
    }
    if (value === CancellationStatus.ACCEPTED) {
      return 'bg-success-200 clr-primary-300';
    }
    return '';
  }

}

@Pipe({
  name: 'statusCancelled',
})
export class StatusCancelledPipe implements PipeTransform {
  transform(value: number): string {
    if (value === CancellationStatus.ISSUE) {
      return 'En attente';
    }
    if (value === CancellationStatus.REFUSED) {
      return 'Refusé';
    }
    if (value === CancellationStatus.ACCEPTED) {
      return 'Accepté';
    }
    return 'Inconnu';
  }
}
