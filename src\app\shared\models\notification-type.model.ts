import { Feedback } from "./feedback";
import { OrderStatus } from "./order";


export interface NotificationMessage {
    _id?: object | string;
    feedback?: Feedback;
    emailAdmin?: string;
    message: string;
    status: number;
    isGeneralNotif?: boolean;
    title: string;
    isSpecificNotif?: boolean;
    img?: string;
    redirect?: string;
    category: NotificationCategory;
    dates: {
        created: Date | number;
        read?: Date;
    };
    smiley?: string;
    orderStatus?: OrderStatus; // Statut de la commande (100, 200, 300, 400, etc.)
    orderReference?: string; // Référence de la commande
}

export enum messageType {
    CREATE = 100,
    READ = 200,
    DELETE = 300
}

export enum NotificationCategory {
    FEEDBACK = 100,
    ORDER = 200,
    GENERAL = 300
}