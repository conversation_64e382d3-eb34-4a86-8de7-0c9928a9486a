import { Component, OnInit } from '@angular/core';
import { NotificationsService } from 'src/app/shared/services/notifications.service';
import { Location } from '@angular/common';
import { NotificationCategory } from 'src/app/shared/models/notification-type.model';
import { StatusOrderItemPipe } from 'src/app/shared/pipes/status-order.pipe';


@Component({
  selector: 'app-notification-detail',
  templateUrl: './notification-detail.component.html',
  styleUrls: ['./notification-detail.component.scss'],
})
export class NotificationDetailComponent implements OnInit {

  constructor(public notificationService: NotificationsService,
    private location: Location,
    ) { }

  ngOnInit() {}

  back() {
    this.location.back();
  }

  // Méthode pour obtenir le message nettoyé de la notification
  getCleanMessage(): string {
    const notification = this.notificationService.currentNotification;
    if (!notification) return '';

    if (notification.category === NotificationCategory.ORDER && notification.orderStatus) {
      const pipe = new StatusOrderItemPipe();
      return pipe.transform(notification.orderStatus, 'message', notification.orderReference);
    }

    return StatusOrderItemPipe.cleanMessage(notification.message, notification.orderStatus, notification.orderReference);
  }
}
