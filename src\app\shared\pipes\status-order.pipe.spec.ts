import { StatusOrderPipe, StatusOrderItemPipe } from './status-order.pipe';
import { OrderStatus } from '../models/order';

describe('StatusOrderPipe', () => {
  it('create an instance', () => {
    const pipe = new StatusOrderPipe();
    expect(pipe).toBeTruthy();
  });
});

describe('StatusOrderItemPipe', () => {
  let pipe: StatusOrderItemPipe;

  beforeEach(() => {
    pipe = new StatusOrderItemPipe();
  });

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should generate correct titles for order statuses', () => {
    expect(pipe.transform(OrderStatus.CREATED, 'title')).toBe('Commande en attente');
    expect(pipe.transform(OrderStatus.VALIDATED, 'title')).toBe('Commande validée');
    expect(pipe.transform(OrderStatus.REJECTED, 'title')).toBe('Commande rejetée');
  });

  it('should generate correct messages for order statuses', () => {
    expect(pipe.transform(OrderStatus.CREATED, 'message')).toBe('Votre commande est en cours de traitement et en attente de validation.');
    expect(pipe.transform(OrderStatus.VALIDATED, 'message')).toBe('Votre commande a été validée avec succès.');
    expect(pipe.transform(OrderStatus.REJECTED, 'message')).toBe('Votre commande a été rejetée. Veuillez contacter le service client pour plus d\'informations.');
  });

  it('should include order reference in messages when provided', () => {
    expect(pipe.transform(OrderStatus.VALIDATED, 'message', 'CMD123')).toBe('Votre commande (CMD123) a été validée avec succès.');
  });

  it('should clean messages with undefined', () => {
    expect(StatusOrderItemPipe.cleanMessage('votre commande a été undefined')).toBe('Votre commande a été validée');
    expect(StatusOrderItemPipe.cleanMessage('commande undefined')).toBe('Commande validée');
  });

  it('should use order status for cleaning when provided', () => {
    expect(StatusOrderItemPipe.cleanMessage('votre commande a été undefined', OrderStatus.REJECTED, 'CMD123'))
      .toBe('Votre commande (CMD123) a été rejetée. Veuillez contacter le service client pour plus d\'informations.');
  });
});
