import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { FidelityProgramPageRoutingModule } from './fidelity-program-routing.module';

import { FidelityProgramPage } from './fidelity-program.page';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from "../../shared/shared.module";
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    FidelityProgramPageRoutingModule,
    SharedModule,
    TranslateModule,
    
   
],
  declarations: [FidelityProgramPage]
})
export class FidelityProgramPageModule {}
