import { Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NotificationCategory, NotificationMessage } from 'src/app/shared/models/notification-type.model';
import { CommonService } from 'src/app/shared/services/common.service';
import { NotificationsService } from 'src/app/shared/services/notifications.service';
import { OrderStatus } from 'src/app/shared/models/order';
import { StatusOrderItemPipe } from 'src/app/shared/pipes/status-order.pipe';

@Component({
  selector: 'app-general-notif-detail',
  templateUrl: './general-notif-detail.component.html',
  styleUrls: ['./general-notif-detail.component.scss'],
})
export class GeneralNotifDetailComponent {
  notificationCategory = NotificationCategory;

  constructor(public notificationService: NotificationsService,
    private location: Location,
    protected commonSrv: CommonService
  ) { }


  back() {
    this.location.back();
  }

  // Méthode pour obtenir le titre nettoyé de la notification
  getCleanTitle(): string {
    const notification = this.notificationService.currentNotification;
    if (!notification) return '';

    if (notification.category === NotificationCategory.ORDER && notification.orderStatus) {
      const pipe = new StatusOrderItemPipe();
      return pipe.transform(notification.orderStatus, 'title', notification.orderReference);
    }

    return StatusOrderItemPipe.cleanMessage(notification.title, notification.orderStatus, notification.orderReference);
  }

  // Méthode pour obtenir le message nettoyé de la notification
  getCleanMessage(): string {
    const notification = this.notificationService.currentNotification;
    if (!notification) return '';

    if (notification.category === NotificationCategory.ORDER && notification.orderStatus) {
      const pipe = new StatusOrderItemPipe();
      return pipe.transform(notification.orderStatus, 'message', notification.orderReference);
    }

    return StatusOrderItemPipe.cleanMessage(notification.message, notification.orderStatus, notification.orderReference);
  }
}
