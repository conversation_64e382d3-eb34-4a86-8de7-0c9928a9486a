<ion-header *ngIf="!isDisableOrderProcess">
  <ion-toolbar class="header">
    <div class="header-title" slot="start">
      <ion-img (click)="back()" slot="start" class="ion-arrow" src="/assets/icons/arrow-blue.svg"></ion-img>
      <ion-title class="title">{{ stepTitle }}</ion-title>
    </div>

    <!-- <div class="header_nav--icon" slot="end">
      <div class="icons_profil">
        <div class="notification">
          <div class="badge"></div>
          <ion-img src="assets/icons/head-notification.svg" alt="" routerLink="/navigation/notifications"></ion-img>
        </div>
        <div>
          <ion-img src="assets/icons/head-message.svg" alt="" routerLink="/navigation/contact"></ion-img>
        </div>
        <div>
          <ion-img src="assets/icons/account.svg" alt="" routerLink="/navigation/account"></ion-img>
        </div>
      </div>
    </div> -->
  </ion-toolbar>
  <ion-toolbar class="step-container">
    <div class="step-content">
      <div *ngFor="let stepI of steps; let i=index;" class="step-item">
        <ion-checkbox mode="ios" [checked]="true" [disabled]="true" *ngIf="stepI === 1"></ion-checkbox>
        <div *ngIf="stepI === 0" class="checkbox">
          <ion-label>{{i+1}}</ion-label>
        </div>
      </div>
    </div>
  </ion-toolbar>
  <div class="space-h-v">
    <!-- <ion-label class="subtitle"
      *ngIf="action != 'search' && action != 'Rechercher' && action !== 'Informations de votre commande' && action !== 'Information of your order'">
      {{action}}
    </ion-label> -->
    <ion-button class="btn btn--meduim"
      *ngIf="action === 'Informations de votre commande' || action === 'Information of your order'" color="primary"
      expand="block">
      <ion-label> {{"order-new-page.information-label" | translate}} </ion-label>
    </ion-button>
  </div>
</ion-header>

<ion-header *ngIf="isDisableOrderProcess">
  <ion-toolbar class="header">
    <div class="header-title" slot="start">
      <ion-img (click)="back()" slot="start" class="ion-arrow" src="/assets/icons/arrow-blue.svg"></ion-img>
      <ion-title class="title">{{"order-new-page.new-order" | translate}}</ion-title>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content class="ion-padding" fullscreen="true" *ngIf="isDisableOrderProcess">
  <div class="no-order-process">
    <div class="icon">
      <ion-img class="ion-text-center" src="assets/images/Phone-maintenance.png"></ion-img>
    </div>
    <div class="text">
      {{"order-new-page.maintenance.first" | translate}} <br>{{"order-new-page.maintenance.second" | translate}} <br>
      {{"order-new-page.maintenance.third" | translate}}
    </div>
  </div>
</ion-content>

<ion-content *ngIf="!isDisableOrderProcess">
  <ion-router-outlet (activate)="onOutletLoaded($event)"></ion-router-outlet>
</ion-content>