<ion-header>
  <ion-toolbar class="header">
    <ion-img (click)="back()" slot="start" src="/assets/icons/arrow-back.svg"></ion-img>
    <ion-title class="title">Détail de la notification</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content>
  <div class="card">

    <div class="notication-detail">
      <img src="assets/logos/cadyst.png" alt="Image absente" class="empty-list-img" />
      <div class="detail">
        <div class="message-contain">
          <div class="title">{{notificationService?.currentNotification?.title | notificationMessageCleaner | truncateString:50 }}</div>
          <span class="date">{{notificationService?.currentNotification?.dates?.created | date:'dd/MM/yy HH:mm:ss'
            }}</span>
        </div>


      </div>
    </div>
    <div class="notication-msg">
      <div class="title">{{notificationService?.currentNotification?.message | notificationMessageCleaner }}</div>
      <img [src]="notificationService?.currentNotification?.img" alt="">
      <ion-button class="btn mbottom250 btn--meduim btn--upper"
        *ngIf="notificationService?.currentNotification?.category === notificationCategory.ORDER" (click)="commonSrv.navigateTo(notificationService?.currentNotification?.redirect)">
        <ion-label class="title"> Consultez détails</ion-label>
      </ion-button>
    </div>
  </div>

</ion-content>