import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientModule } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { TruncateStringPipe } from './pipes/truncate-string.pipe';
import { AuthorizationRemovalPipe } from './pipes/authorization-removal.pipe';
import { TransformEnumToStringPipe } from './pipes/transform-enum-to-string.pipe';
import { ColorStatusCancelledPipe, ColorStatusOrderPipe, ColorStatusOrderRetailPipe,StatusCancelledPipe, StatusOrderPipe, StatusOrderRetailPipe } from './pipes/status-order.pipe';
import { ItemsGroupComponent } from './components/items-group/items-group.component';
import { HeaderConnectComponent } from './components/header-connect/header-connect.component';
import { ProgressSpinnerComponent } from './components/progress-spinner/progress-spinner.component';
import { ReloadBalanceColorStatusPipe, ReloadBalanceStatusPipe } from './pipes/reload-balance.pipe';
import { BottomSheetValidationActionComponent } from './components/bottom-sheet-validation-action/bottom-sheet-validation-action.component';
import { ColorCompaniesPipe } from './pipes/color-companies.pipe';
import { LabelCompaniesPipe } from './pipes/label-companies.pipe';
import { ProductCardComponent } from './components/product-card/product-card.component';
import { ProductHomeCardComponent } from './components/product-home-card/product-home-card.component';
import { TruncatePackagingLabelPipe } from './pipes/truncate-packaging-label.pipe';
import { GetTotalQuantityOfProductsInCartPipe } from './pipes/get-total-quantity-of-products-in-cart.pipe';
import { HeaderActionsComponent } from './components/header-actions/header-actions.component';
import { ProductsSliderComponent } from './components/products-slider/products-slider.component';
import { NavbarComponent } from './components/navbar/navbar.component';
import { ChangeLanguageComponent } from './components/change-language/change-language.component';
import { BaseModalComponent } from './components/base-modal/base-modal.component';
import { PhoneFormatDirective } from './directives/phone-format.directive';
import { ClaimStatusPipe, ClaimStatusColorPipe } from './pipes/claim-status.pipe';
import { BottomSheetValidationOrderComponent } from './components/bottom-sheet-validation-order/bottom-sheet-validation-order.component';
import { CapitalizePipe } from './pipes/capitalize.pipe';
import { NotificationMessageCleanerPipe } from './pipes/notification-message-cleaner.pipe';
import { PopoverComponentComponent } from './components/popover-component/popover-component.component';
import { ProductHomeParticularCardComponent } from './components/product-home-particular-card/product-home-particular-card.component';
import { FidelityProgramPipe, LoyaltyProgramPipe, LoyaltyProgramClassColorPipe } from './pipes/fidelity-program.pipe';
import { BottomSheetComponent } from './components/bottom-sheet/bottom-sheet.component';
import { SendSponsorComponent } from './components/send-sponsor/send-sponsor.component';
import { ReferralConfirmationModalComponent } from './components/referral-confirmation-modal/referral-confirmation-modal.component';
import { GetTotalPointsPipe } from './pipes/get-total-points.pipe';
import { QrCodeScannerComponent } from './components/qr-code-scanner/qr-code-scanner.component';
import { PurchaseSummaryComponent } from './components/purchase-summary/purchase-summary.component';
import { ProductCartQrOrdersComponent } from './components/product-cart-qr-orders/product-cart-qr-orders.component';
import { QuantityProductPipe } from './pipes/quantity-product.pipe';
import { AppVersion } from '@awesome-cordova-plugins/app-version/ngx';


@NgModule({
  declarations: [
    NavbarComponent,
    StatusOrderPipe,
    TruncateStringPipe,
    ItemsGroupComponent,
    ColorStatusOrderPipe,
    StatusOrderRetailPipe,
    HeaderConnectComponent,
    HeaderActionsComponent,
    ProductsSliderComponent,
    ReloadBalanceStatusPipe,
    AuthorizationRemovalPipe,
    ProgressSpinnerComponent,
    TransformEnumToStringPipe,
    ReloadBalanceColorStatusPipe,
    BottomSheetValidationActionComponent,
    ColorCompaniesPipe,
    LabelCompaniesPipe,
    ColorStatusCancelledPipe,
    StatusCancelledPipe,
    ProductCardComponent,
    ProductHomeCardComponent,
    ProductHomeParticularCardComponent,
    TruncatePackagingLabelPipe,
    GetTotalQuantityOfProductsInCartPipe,
    NavbarComponent,
    ChangeLanguageComponent,
    BaseModalComponent,
    PhoneFormatDirective,
    ClaimStatusPipe,
    ClaimStatusColorPipe,
    BottomSheetValidationOrderComponent,
    CapitalizePipe,
    PopoverComponentComponent,
    FidelityProgramPipe,
    LoyaltyProgramPipe,
    LoyaltyProgramClassColorPipe,
    BottomSheetComponent,
    SendSponsorComponent,
    ReferralConfirmationModalComponent,
    GetTotalPointsPipe,
    QrCodeScannerComponent,
    PurchaseSummaryComponent,
    ProductCartQrOrdersComponent,
    QuantityProductPipe,
    ColorStatusOrderRetailPipe,
    NotificationMessageCleanerPipe
  ],
  exports: [
    CapitalizePipe,
    StatusOrderPipe,
    NavbarComponent,
    ClaimStatusPipe,
    NavbarComponent,
    TruncateStringPipe,
    ColorCompaniesPipe,
    LabelCompaniesPipe,
    ItemsGroupComponent,
    PhoneFormatDirective,
    ClaimStatusColorPipe,
    ColorStatusOrderPipe,
    ProductCardComponent,
    ProductCartQrOrdersComponent,
    StatusOrderRetailPipe,
    HeaderConnectComponent,
    QrCodeScannerComponent,
    HeaderActionsComponent,
    ProductsSliderComponent,
    PurchaseSummaryComponent,
    PopoverComponentComponent,
    ReloadBalanceStatusPipe,
    AuthorizationRemovalPipe,
    ProgressSpinnerComponent,
    ProductHomeCardComponent,
    ProductHomeParticularCardComponent,
    ColorStatusCancelledPipe,
    StatusCancelledPipe,
    LoyaltyProgramPipe,
    TransformEnumToStringPipe,
    TruncatePackagingLabelPipe,
    ReloadBalanceColorStatusPipe,
    BottomSheetValidationOrderComponent,
    GetTotalQuantityOfProductsInCartPipe,
    BottomSheetValidationActionComponent,
    LoyaltyProgramClassColorPipe,
    BottomSheetComponent,
    ReferralConfirmationModalComponent,
    GetTotalPointsPipe,
    QuantityProductPipe,
    ColorStatusOrderRetailPipe,
    NotificationMessageCleanerPipe
  ],
  imports: [
    FormsModule,
    IonicModule,
    CommonModule,
    TranslateModule,
    HttpClientModule,
    ReactiveFormsModule,
  ],
  providers: [AppVersion],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SharedModule { }
