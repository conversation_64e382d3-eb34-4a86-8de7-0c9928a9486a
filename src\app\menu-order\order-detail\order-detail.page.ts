import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { <PERSON><PERSON><PERSON><PERSON>roller, ModalController, PopoverController } from '@ionic/angular';
import { ActivatedRoute, Router } from '@angular/router';
import { OrderService } from '../services/order.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { CancellationRequest, CancellationStatus, Order, OrderAction, OrderStatus, Carrier } from 'src/app/shared/models/order';
import { CartItem } from 'src/app/shared/models/cart.model';
import { Language } from 'src/app/shared/enum/language.enum';
import { EmployeeType, UserCategory } from 'src/app/shared/enum/user-category.enum';
import { PopoverComponentComponent } from 'src/app/shared/components/popover-component/popover-component.component';
import { BaseModalComponent } from 'src/app/shared/components/base-modal/base-modal.component';
import { BottomSheetValidationOrderComponent } from 'src/app/shared/components/bottom-sheet-validation-order/bottom-sheet-validation-order.component';
import { ModalProductDetailComponent } from './modal-product-detail/modal-product-detail.component';
import { HttpErrorResponse } from '@angular/common/http';
import { OrderRetailService } from 'src/app/shared/services/order-retail.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MarketPlaceService } from '../services/market-place.service';

@Component({
  selector: 'app-order-detail',
  templateUrl: './order-detail.page.html',
  styleUrls: ['./order-detail.page.scss'],
})
export class OrderDetailPage {
  slideProductOpts = {
    initialSlide: 0,
    speed: 400,
    slidesPerView: 4,
    spaceBetween: 10,
  };

  orders: Order[];
  isValidate = false;
  isReject = false;
  isLoading = false;
  isPopoverOpen = false;
  order: Order;
  editMode = false;
  carrierForm: FormGroup = new FormGroup({});
  carrier: Carrier;


  orderStatus = OrderStatus;
  userCategory = UserCategory;
  orderAction = OrderAction;
  employeeType = EmployeeType;


  private actionHandlers = {
    modifier: () => this.modifyOrder(),
    telecharger: () => this.generatePurchaseOrder(this.orderService.order._id),
    annuler: () => this.handleCancellation(),
  };

  constructor(
    private location: Location,
    public orderService: OrderService,
    public orderRetailService: OrderRetailService,
    public marketService : MarketPlaceService,
    private alertController: AlertController,
    public popoverCtrl: PopoverController,
    public commonSrv: CommonService,
    private modalCtrl: ModalController,
    private translateService: TranslateConfigService,
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,

  ) { }

  async ionViewWillEnter(): Promise<void> {

    if (!this.orderService.order && !this.route.snapshot.params['idOrder']) return this.back();

    if (!this.orderService.order) {
      const id = this.route.snapshot.params['idOrder'];
      this.orderService.order = await this.orderService.find(id);
      this.orderRetailService.orderRetail = await this.orderRetailService.findOrderRetail(id);
      this.marketService.order = await this.marketService.find(id);
    }
    this.initForm();
    this.loadCarrierData();
  }

  trackByFn = (index: any, item: any): any => index;

  back(): void {
    this.orderService.order = null;
    this.orderRetailService.orderRetail = null;
    this.marketService.order = null;
    this.location.back();
  }

  showModalRemovalDetail(order: Order[]): void {
    this.orders = order;
    this.orderService.modalDetailRemoval = true;
  }

  async presentPopover(ev: Event): Promise<void> {
    const popover = await this.popoverCtrl.create({
      component: PopoverComponentComponent,
      event: ev,
      cssClass: 'custom-popover',
      mode: 'md',
      componentProps: {
        actions: [
          {
            label: this.getTranslatedText('Modifier la commande', 'Edit order'),
            action: 'modifier'
          },
          {
            label: this.getTranslatedText('Télécharger Bon de co..', 'Download PO'),
            action: 'telecharger'
          },
          {
            label: this.getTranslatedText('Demande d\'annulation', 'Cancellation request'),
            action: 'annuler'
          },
        ]
      }
    });
    await popover.present();

    const { data } = await popover.onWillDismiss();
    if (!data) return;
    this.handlePopoverAction(data);
  }

  handlePopoverAction(action: string): void {
    const handler = this.actionHandlers[action];
    if (!handler) return;
    handler();
  }

  modifyOrder(): void {
    if (!this.canModifyOrder()) {
      this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? `Une erreur est survenue.`
          : `An error occurred.`,
        color: 'warning'
      });
      return;
    }
    this.navigateToOrderUpdate();
  }


  private canModifyOrder(): boolean {
    return this.orderService.order.status !== OrderStatus.VALIDATED &&
      this.commonSrv.user.category !== UserCategory.Commercial;
  }

  private navigateToOrderUpdate(): void {
    const orderId = this.orderService.order._id;
    this.router.navigate([`order/detail/${orderId}/order-update`])
      .then(success => console.log(success ? 'Navigation réussie' : 'Échec de la navigation'))
      .catch(err => console.error('Erreur lors de la navigation', err));
  }

  private cancelOrder(): void {
    this.commonSrv.showToast({
      message: this.translateService.currentLang === Language.French
        ? `Une erreur est survenue.`
        : `An error occurred.`,
      color: 'warning'
    });
  }

  async showModalConfirmValidation(): Promise<void> {
    const validOrder = await this.modalCtrl.create({
      component: BottomSheetValidationOrderComponent,
      cssClass: 'modal',
      initialBreakpoint: 0.4,
      breakpoints: [0, 0.75, 0.5],
      mode: 'ios',
      componentProps: {
        statusToUpdate: OrderStatus.PAID,
        order: this.orderService?.order
      },
    });
    await validOrder.present();

    const { data } = await validOrder.onWillDismiss();

    if (data) {
      this.updateOrderStatus(data);
    }
  }

  private updateOrderStatus(data: { status: OrderStatus; reference: string }): void {
    this.orderService.order.status = data.status;
    this.orderService.order.reference = data.reference;
  }

  async showModalRejectOrder(): Promise<void> {
    const alert = await this.alertController.create({
      header: this.getTranslatedText('Rejet de commande', 'Reject Order'),
      message: this.getTranslatedText(
        'Vous êtes sur le point de rejeter cette commande.\n Confirmez vous cette action ?',
        'You are about to reject this order.\n Do you confirm this action ?'
      ),
      cssClass: 'custom-loading',
      buttons: [
        {
          text: this.getTranslatedText('Annuler', 'Cancel'),
          cssClass: 'alert-button-cancel',
        },
        {
          text: this.getTranslatedText('Rejeter', 'Reject'),
          cssClass: 'alert-button-confirm',
          handler: () => this.rejectOrder(),
        },
      ],
    });
    await alert.present();
  }

  private async rejectOrder(): Promise<void> {
    await this.orderService.RhRejectOrder(this.orderService.order);
    this.isReject = true;
    this.isValidate = true;
  }

  async showDetail(item: CartItem): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: ModalProductDetailComponent,
      cssClass: 'modal',
      initialBreakpoint: 0.35,
      breakpoints: [0, 0.75, 0.8, 0.35, 0.9, 0.95, 1],
      mode: 'ios',
      componentProps: { item, packaging: item?.packaging },
    });
    await modal.present();
  }

  async generatePurchaseOrder(id: string): Promise<void> {
    if (!id) {
      console.log('ID de commande non disponible');
      return;
    }

    this.isLoading = true;
    try {
      const res = await this.orderService.generatePurchaseOrder(id);
      if (res?.pdfPath) {
        window.open(res.pdfPath, '_blank');
      } else {
        console.log('Chemin du PDF non disponible');
      }
    } catch (error) {
      console.error('Erreur lors de la génération du bon de commande:', error);
    } finally {
      setTimeout(() => { this.isLoading = false; }, 1000);
    }
  }

  private getTranslatedText(frText: string, enText: string): string {
    return this.translateService.currentLang === Language.French ? frText : enText;
  }

  async handleCancellation() {
    // Vérifier si nous avons une commande valide
    if (!this.order?._id) {
      await this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? 'La commande n\'est pas correctement chargée'
          : 'Order is not properly loaded',
        color: 'warning'
      });
      return;
    }

    let defaultMessage = this.translateService.currentLang === Language.French
      ? 'À la demande du client'
      : 'At the customers request';

    const modal = await this.modalCtrl.create({
      component: BaseModalComponent,
      cssClass: 'modalClass',
      componentProps: {
        dataModal: {
          confirmButton: 'Envoyer',
          cancelButton: 'Annuler',
          text: 'Demmande d annulation de la commande',
          message: 'Renseigner le motif de la demande',
          isAnnulation: true,
          handler: (messageFromModal: string) => {
            defaultMessage = messageFromModal || defaultMessage;
            this.processCancellationRequest(defaultMessage);
          }
        }
      }
    });

    await modal.present();

    const { role } = await modal.onWillDismiss();
    if (role === 'confirm') {
      this.showFinalConfirmation(defaultMessage);
    }
  }

  async processCancellationRequest(message: string) {
    if (!this.order?._id) {
      await this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? 'La commande n\'est pas correctement chargée'
          : 'Order is not properly loaded',
        color: 'warning'
      });
      return;
    }

    try {
      this.isLoading = true;

      this.order.messageCancellation = message;
    } catch (error) {
      await this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? 'Une erreur est survenue lors du traitement de votre demande.'
          : 'An error occurred while processing your request.',
        color: 'danger'
      });
    } finally {
      this.isLoading = false;
    }
  }

  async sendCancellationRequest(message: string) {
    if (!this.order?._id) {
      await this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? 'La commande n\'est pas correctement chargée'
          : 'Order is not properly loaded',
        color: 'warning'
      });
      return;
    }

    try {
      this.isLoading = true;

      const cancellationData: CancellationRequest = {
        messageCancellation: message,
        cancellationStatus: CancellationStatus.ISSUE
      };

      const response = await this.orderService.cancellationOrder(
        this.order._id,
        cancellationData
      );

      if (response instanceof HttpErrorResponse) {
        await this.commonSrv.showToast({
          message: this.translateService.currentLang === Language.French
            ? `Une erreur est survenue. ${response?.message}`
            : `An error occurred. ${response?.message}`,
          color: 'warning'
        });
        return;
      }

      if (this.order) {
        this.order.messageCancellation = message;
        this.order.cancellationStatus = CancellationStatus.ISSUE;
      }

      await this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? `Demande d'annulation pour la commande ${this.order.appReference} envoyée avec succès`
          : `Cancellation request for order ${this.order.appReference} sent successfully`,
        color: 'success'
      });

      this.router.navigateByUrl('order/history');
    } catch (error) {
      await this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? 'Une erreur est survenue lors de l\'envoi de la demande.'
          : 'An error occurred while sending the request.',
        color: 'danger'
      });
    } finally {
      this.isLoading = false;
    }
  }

  private async showFinalConfirmation(message: string) {
    const confirmModal = await this.modalCtrl.create({
      component: BaseModalComponent,
      cssClass: 'modalClass',
      componentProps: {
        dataModal: {
          confirmButton: 'Confirmer',
          cancelButton: 'Annuler',
          text: 'Êtes-vous sûr de vouloir envoyer cette demande d\'annulation ?',
          handler: () => {
            this.sendCancellationRequest(message);
          }
        }
      }
    });

    await confirmModal.present();
  }

  initForm() {
    this.carrierForm = this.fb.group({
      name: [''],
      phone: [''],
      idCard: [''],
      vehicleCategory: [''],
      vehiclePlate: ['']
    });
  }

  toggleEditMode() {
    this.editMode = !this.editMode;

    if (this.editMode) {
      this.loadCarrierData();
    }
  }

  loadCarrierData() {
    const carrier: Carrier = this.orderService?.order?.carrier || {
      _id: '',
      name: '',
      phone: '',
      idCard: '',
      vehicleCategory: '',
      vehiclePlate: '',
      driverLicense: ''
    };

    this.carrierForm.patchValue({
      name: carrier?.name,
      phone: carrier?.phone,
      idCard: carrier?.idCard,
      vehicleCategory: carrier?.vehicleCategory,
      vehiclePlate: carrier?.vehiclePlate
    });
  }

  async saveCarrier() {
    const updatedCarrier = this.carrierForm.value;
    const orderId = this.orderService?.order?._id;

    if (!orderId) {
      console.error("ID de commande introuvable");
      return;
    }

    try {
      const result = await this.orderService.updateCarrier(orderId, updatedCarrier);
      console.log("Transporteur mis à jour :", result);
      this.orderService.order.carrier = updatedCarrier;
      this.editMode = false;
    } catch (error) {
      console.error("Erreur lors de la mise à jour du transporteur :", error);
    }
  }

}