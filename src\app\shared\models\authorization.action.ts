export enum CompanyAction {
  CREATE = 'create_company',
  UPDATE = 'update_company',
  DELETE = 'delete_company',
  VIEW = 'view_company',
  ADD_USER = 'add_user',
  VIEW_USERS = 'view_company_user'
}

export enum UserAction {
  CREATE = 'create_user',
  UPDATE = 'update_user',
  DELETE = 'delete_user',
  VIEW = 'view_user',
  CHANGE_PASSWORD = 'change_password',
  VALIDATE_USER = 'validate_user'
}

export enum OrderSupplierAction {
  CREATE = 'create_oder_supplier',
  UPDATE = 'update_oder_supplier',
  DELETE = 'delete_oder_supplier',
  VIEW = 'view_oder_supplier',
}

export enum QrCodeAction {
  CREATE = 'create_qr_code',
  UPDATE = 'update_qr_code',
  DELETE = 'delete_qr_code',
  VIEW = 'view_qr_code',
}