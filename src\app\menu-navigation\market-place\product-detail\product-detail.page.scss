@import "src/theme/mixins.scss";

ion-toolbar {
    --padding-start: var(--space-5);
    --padding-end: var(--space-5);
    --padding-top: var(--space-5);
    --border-color: transparent;
    --background-color: transparent;

    .div-start {
        @include v-h-between;

        ion-img {
            width: $padding-4;
            margin-right: calc(25 * var(--res));
        }

        .title {
            font-family: var(--mont-bold);
            text-align: start;
            color: var(--clr-primary-900);

        }
    }

    ion-header {
        background: #f4f4f4;

        .title,
        .subtitle {
            font-family: $font-regular;
        }

        .title {
            color: $color-fiftheen;
            font-size: calc(45 * var(--res));
        }

        .subtitle {
            color: black;
            font-size: calc(45 * var(--res));
        }

        .space-h-v {
            margin: $padding-5 0;
            padding: 0 $padding-5;

            ion-button {
                &::part(native) {
                    color: var(--clr-primary-700);
                    background: var(--clr-primary-0);
                }

                ion-label {
                    font-family: var(--mont-bold);
                }
            }
        }

        .searchbar {
            --background: transparent;
            height: calc(5 * var(--resH));
            border: none;
            --box-shadow: none;
            border-bottom: 1px solid #1e1e1e;
        }

        .btn {
            text-transform: initial;
        }

        ion-toolbar {
            --border-color: transparent;
            padding: $padding-5 $padding-5 0 $padding-5;
            --background: #f4f4f4;
        }

        .header {
            @include v-align;

            .header-title {
                @include v-align;

                ion-img {
                    width: $padding-4;
                    margin-right: calc(25 * var(--res));

                }

                .ion-arrow {
                    height: 20px;
                    width: 25px;
                }

                .title {
                    font-size: calc(46 * var(--res));
                    text-align: start;
                    color: $color-fiftheen;
                }
            }

            .icons_profil {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: calc(31 * var(--res));
                width: 100px;

                ion-img {
                    width: calc(60 * var(--res));
                }

                .notification {
                    text-align: right;
                    position: relative;

                    .badge {
                        position: absolute;
                        right: 18%;
                        width: calc(20 * var(--res));
                        height: calc(20 * var(--res));
                        border-radius: 50%;
                        background: rgb(173, 5, 5);
                    }
                }
            }
        }
    }

}

#container {
    padding: 0px;
    background-color: $color-nine;

    .item-detail-focus {
        padding: 0 $padding-3;
        padding-bottom: $padding-3;

        background-color: $color-nine;
        border-radius: 0px 0px calc(35 * var(--res)) calc(35 * var(--res));

        ion-item {
            --padding-start: 0px;
            --inner-padding-end: 0px;
            margin-bottom: 0.7em;

            ion-slides {
                height: calc(36.5 * var(--resH));
                padding-bottom: 1em;
                width: 100%;

                .swiper-container-horizontal {
                    .swiper-pagination-bullets {
                        bottom: 0px !important;
                        left: 0;
                        width: 100%;
                        justify-content: center;
                    }
                }

                ion-slide {
                    ion-img {
                        height: 100%;
                        width: 100%;
                    }
                }
            }
        }

        p {
            font-size: 12px;
        }

        .row {
            margin-bottom: 1em;
            @include v-h-between;

            .detail-item {
                .title {
                    font-size: 14px;
                    color: $color-primary;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    letter-spacing: -0.165px;
                }

                .stars {
                    margin-top: 0.25em;
                    display: flex;
                    // align-items: center;

                    ion-icon {
                        height: 0.75em;
                        width: 0.75em;
                    }

                    ion-text {
                        margin-left: 0.55em;
                    }

                    .small-stars {
                        font-size: 17px;
                    }

                    .small-text {
                        font-size: 10px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: normal;
                        letter-spacing: -0.165px;
                    }
                }
            }

            .btn-group {
                ion-button {
                    width: calc(4 * var(--resH));
                    height: calc(4 * var(--resH));
                    --padding-end: 0;
                    --padding-start: 0;
                    --border-radius: 50%;
                }

                .btn-border {
                    margin-left: 0.5em;
                    --border-color: #d9d9d9;
                    --border-style: solid;
                    --border-width: 1px;
                    width: 25px;
                    height: 25px;
                }
            }
        }

        .pay-btn{
            height: 40px;
        }

    }
}
