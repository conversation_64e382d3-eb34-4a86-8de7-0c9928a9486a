import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { NotificationCategory, NotificationMessage, messageType } from '../../models/notification-type.model';
import { StorageService } from '../../services/storage.service';
import { NotificationsService } from '../../services/notifications.service';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { UserService } from '../../services/user.service';
import { CommonService } from '../../services/common.service';
import { User } from '../../models/user.models';
import { TranslateService } from '@ngx-translate/core';
import { OrderStatus } from '../../models/order';
import { StatusOrderItemPipe } from '../../pipes/status-order.pipe';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.page.html',
  styleUrls: ['./notifications.page.scss'],
})
export class NotificationsPage implements OnInit {
  dataNotification: NotificationMessage[];
  notificationCategory = NotificationCategory;
  isLoading = false;
  isDeletingLoading = false;
  user: User;
  isSelectionMode = false;
  selectedNotifications: Set<string> = new Set<string>();
  selectedCount = 0;

  constructor(
    private notificationService: NotificationsService,
    private storageService: StorageService,
    private userSrv: UserService,
    private location: Location,
    private router: Router,
    private commonSrv: CommonService,
    private translateService: TranslateService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit() { }

  async ionViewWillEnter() {
    this.isLoading = true;
    this.user = this.storageService.getUserConnected();
    const userNotif = await this.userSrv?.find(this.user?._id);
    const query = {
      email: this.user?.email,
      userId: userNotif?._id
    };
    const userNotification = userNotif?.notifications?.filter((notification: { status: messageType; }) => notification?.status !== messageType.DELETE);
    query['notifications'] = JSON.stringify({ $in: userNotification?.map((notification: { id: any; }) => notification?.id) });
    this.dataNotification = (await this.notificationService.getMessages(query))?.data;

    this.dataNotification?.forEach(notification => {
      const userNotification = userNotif?.notifications?.find((notif: { id: string; }) => notif?.id === notification?._id?.toString());
      if (userNotification) notification.status = userNotification?.status;

      // Extraire le statut de commande et la référence
      this.extractOrderInfo(notification);
    });
    this.isLoading = false;
  }

  manageStatusNotification(status: any): any {
    if (status == 100) return { 'background': `rgb(203 5 58)` };
  }

  notifications(status: any): any {
    if (status != 100) return { 'background': `white` };
  }

  async showDetailNotification(notification: NotificationMessage) {
    // S'assurer que les informations de commande sont extraites
    this.extractOrderInfo(notification);

    this.notificationService.currentNotification = notification;
    if (this.notificationService.currentNotification?.isGeneralNotif ||
      this.notificationService.currentNotification?.category === this.notificationCategory.ORDER ||
      this.notificationService.currentNotification?.category === this.notificationCategory.FEEDBACK) {
      this.router.navigate(['/navigation/notifications/general-detail']);
    } else {
      this.router.navigate(['/navigation/notifications/detail']);
    }
    if (notification.status == messageType.CREATE) await this.notificationService.makeRead(notification?._id);
  }

  back() {
    this.location.back();
  }

  toggleSelectionMode() {
    this.isSelectionMode = !this.isSelectionMode;
    if (!this.isSelectionMode) {
      this.selectedNotifications.clear();
      this.updateSelectedCount();
    }
    this.cdr.detectChanges();
  }

  toggleNotificationSelection(notificationId: string) {
    this.selectedNotifications.has(notificationId)
      ? this.selectedNotifications.delete(notificationId)
      : this.selectedNotifications.add(notificationId);
    this.updateSelectedCount();
    this.cdr.detectChanges();
  }

  selectAllNotifications() {
    this.selectedNotifications.size === this.dataNotification?.length
      ? this.selectedNotifications.clear()
      : this.dataNotification.forEach((notification) => {
        if (notification?._id) {
          const id = notification?._id?.toString();
          this.selectedNotifications.add(id);
        }
      });
    this.updateSelectedCount();
    this.cdr.detectChanges();
  }

  private updateSelectedCount() {
    this.selectedCount = this.selectedNotifications.size;
    this.cdr.detectChanges();
  }

  async deleteSelectedNotifications() {
    if (this.selectedNotifications.size === 0) return;
    this.isDeletingLoading = true;
    const ids = Array.from(this.selectedNotifications);
    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));

      await this.notificationService.deleteNotifications(ids).catch((error) => {
        if (error.status === 404) {
          throw new Error('Route not found');
        }
        throw error;
      });

      const deletedCount = ids.length;
      this.dataNotification = this.dataNotification.filter((notification) =>
        !ids.includes(notification?._id?.toString() ?? '')
      );

      this.selectedNotifications.clear();
      this.updateSelectedCount();
      this.isSelectionMode = false;

      await this.commonSrv.showToast({
        message: this.translateService.currentLang === 'fr'
          ? `${deletedCount} notification(s) supprimée(s) avec succès`
          : `${deletedCount} notification(s) successfully deleted`,
        color: 'success'
      });

    } catch (error) {
      console.error('Error deleting notifications:', error);

      await this.ionViewWillEnter();

      await this.commonSrv.showToast({
        message: this.translateService.currentLang === 'fr'
          ? 'Une erreur est survenue lors de la suppression des notifications'
          : 'An error occurred while deleting notifications',
        color: 'danger'
      });
    } finally {
      this.isDeletingLoading = false;
      this.cdr.detectChanges();
    }
  }

  async deleteNotification(notificationId: string) {
    if (!notificationId) return;

    this.isDeletingLoading = true;

    try {
      await new Promise((resolve) => setTimeout(resolve, 2000));
      await this.notificationService.deleteNotifications([...notificationId]).catch((error) => {
        if (error.status === 404) {
          throw new Error('Delete operation failed');
        }
        throw error;
      });

      // Si on arrive ici, c'est que la suppression a réussi
      this.dataNotification = this.dataNotification.filter(
        (notification) => notification?._id?.toString() !== notificationId
      );

      if (this.selectedNotifications.has(notificationId)) {
        this.selectedNotifications.delete(notificationId);
        this.updateSelectedCount();
      }

      await this.commonSrv.showToast({
        message: this.translateService.currentLang === 'fr'
          ? 'Notification supprimée avec succès'
          : 'Notification successfully deleted',
        color: 'success'
      });

    } catch (error) {
      console.error('Error deleting notification:', error);
      await this.ionViewWillEnter();

      await this.commonSrv.showToast({
        message: this.translateService.currentLang === 'fr'
          ? 'Une erreur est survenue lors de la suppression de la notification'
          : 'An error occurred while deleting the notification',
        color: 'danger'
      });
    } finally {
      this.isDeletingLoading = false;
      this.cdr.detectChanges();
    }
  }

  // Méthode pour extraire les informations de commande de la notification
  private extractOrderInfo(notification: NotificationMessage) {
    if (notification.category !== NotificationCategory.ORDER) {
      return;
    }

    // Essayer d'extraire le statut de commande du redirect URL
    if (notification.redirect) {
      const orderStatusMatch = notification.redirect.match(/status[=:](\d+)/i);
      if (orderStatusMatch) {
        notification.orderStatus = parseInt(orderStatusMatch[1], 10);
      }

      // Essayer d'extraire la référence de commande du redirect URL
      const orderRefMatch = notification.redirect.match(/(?:ref|reference|id)[=:]([^&]+)/i);
      if (orderRefMatch) {
        notification.orderReference = orderRefMatch[1];
      }
    }

    // Si pas trouvé dans le redirect, essayer d'analyser le message
    if (!notification.orderStatus && notification.message) {
      // Rechercher des mots-clés dans le message pour déterminer le statut
      const message = notification.message.toLowerCase();

      if (message.includes('validé') || message.includes('approuvé')) {
        notification.orderStatus = OrderStatus.VALIDATED;
      } else if (message.includes('rejeté') || message.includes('refusé')) {
        notification.orderStatus = OrderStatus.REJECTED;
      } else if (message.includes('en attente') || message.includes('traitement')) {
        notification.orderStatus = OrderStatus.CREATED;
      } else {
        // Valeur par défaut pour les commandes marketplace
        notification.orderStatus = OrderStatus.CREATED; // 100 = en attente
      }
    }

    // Essayer d'extraire la référence du message si pas encore trouvée
    if (!notification.orderReference && notification.message) {
      const refMatch = notification.message.match(/(?:commande|référence|ref)[:\s]+([A-Z0-9-]+)/i);
      if (refMatch) {
        notification.orderReference = refMatch[1];
      }
    }
  }

  // Méthode pour obtenir le titre nettoyé de la notification
  getCleanTitle(notification: NotificationMessage): string {
    if (notification.category === NotificationCategory.ORDER && notification.orderStatus) {
      const pipe = new StatusOrderItemPipe();
      return pipe.transform(notification.orderStatus, 'title', notification.orderReference);
    }

    // Pour les autres types de notifications, nettoyer le titre existant
    return StatusOrderItemPipe.cleanMessage(notification.title, notification.orderStatus, notification.orderReference);
  }

  // Méthode pour obtenir le message nettoyé de la notification
  getCleanMessage(notification: NotificationMessage): string {
    if (notification.category === NotificationCategory.ORDER && notification.orderStatus) {
      const pipe = new StatusOrderItemPipe();
      return pipe.transform(notification.orderStatus, 'message', notification.orderReference);
    }

    // Pour les autres types de notifications, nettoyer le message existant
    return StatusOrderItemPipe.cleanMessage(notification.message, notification.orderStatus, notification.orderReference);
  }

  // Méthode de test pour démontrer le fonctionnement
  testNotificationCleaning() {
    console.log('=== Test de nettoyage des notifications ===');

    // Exemple 1: Commande en attente (statut 100)
    const notification1: NotificationMessage = {
      _id: '1',
      title: 'Commande undefined',
      message: 'Votre commande a été undefined',
      category: NotificationCategory.ORDER,
      orderStatus: OrderStatus.CREATED, // 100
      orderReference: 'CMD123',
      status: 100,
      dates: { created: Date.now() }
    };

    console.log('Avant:', notification1.title, '|', notification1.message);
    console.log('Après:', this.getCleanTitle(notification1), '|', this.getCleanMessage(notification1));

    // Exemple 2: Commande validée (statut 300)
    const notification2: NotificationMessage = {
      _id: '2',
      title: 'Commande undefined',
      message: 'Votre commande a été undefined',
      category: NotificationCategory.ORDER,
      orderStatus: OrderStatus.VALIDATED, // 300
      orderReference: 'CMD456',
      status: 100,
      dates: { created: Date.now() }
    };

    console.log('Avant:', notification2.title, '|', notification2.message);
    console.log('Après:', this.getCleanTitle(notification2), '|', this.getCleanMessage(notification2));

    // Exemple 3: Commande rejetée (statut 400)
    const notification3: NotificationMessage = {
      _id: '3',
      title: 'Commande undefined',
      message: 'Votre commande a été undefined',
      category: NotificationCategory.ORDER,
      orderStatus: OrderStatus.REJECTED, // 400
      orderReference: 'CMD789',
      status: 100,
      dates: { created: Date.now() }
    };

    console.log('Avant:', notification3.title, '|', notification3.message);
    console.log('Après:', this.getCleanTitle(notification3), '|', this.getCleanMessage(notification3));
  }
}