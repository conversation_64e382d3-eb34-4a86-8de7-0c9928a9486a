<ion-header [translucent]="true">
  <div class="header">
    <ion-img slot="start" (click)="back()" src="/assets/icons/arrow-back.svg"></ion-img>
    <ion-title> Liste des utilisateurs </ion-title>
</div>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="container">
      <a [routerLink]="['/navigation/companies-account']" class="item" *ngIf="companyAction.VIEW">
        <ion-img src="/assets/images/man.png"></ion-img>
        <ion-label>
            {{ "user-list.direct" | translate }}
        </ion-label>
        <ion-icon slot="end" name="chevron-forward"></ion-icon>
      </a>

      <a [routerLink]="['/navigation/indirect-user']" class="item" *ngIf="userAction.VIEW">
        <ion-img src="/assets/images/man.png"></ion-img>
        <ion-label>
            {{ "user-list.indirect" | translate }}
        </ion-label>
        <ion-icon slot="end" name="chevron-forward"></ion-icon>
      </a>
  </div>
</ion-content>
