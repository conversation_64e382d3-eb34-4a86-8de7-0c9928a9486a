import { Router } from '@angular/router';
import { Cart } from 'src/app/shared/models/cart.model';
import { ProductService } from 'src/app/menu-order/services/product.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { OrderService } from 'src/app/menu-order/services/order.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { Language } from 'src/app/shared/enum/language.enum';
import { Component, OnInit, inject } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { OrderSupplier } from 'src/app/shared/models/order-particular';
import { ScannerService } from 'src/app/shared/services/scanner.service';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { FidelityProgramService } from 'src/app/shared/services/fidelity-program.service';
import { Company } from 'src/app/shared/models/company.model';
import { Location } from '@angular/common';
import { Particular, ParticularCategory, User } from 'src/app/shared/models/user.models';
import { QrCodeService } from 'src/app/shared/services/qr-code.service';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';

@Component({
  selector: 'app-recap-scan',
  templateUrl: './recap-scan.page.html',
  styleUrls: ['./recap-scan.page.scss'],
})
export class RecapScanPage implements OnInit {
  cart: Cart;
  isLoading: boolean;
  supplier: Company;
  user: Particular
  private orderSrv = inject(OrderService);
  private scannerSrv = inject(ScannerService);
  private qrcodeSrv = inject(QrCodeService);
  storageService = inject(StorageService);
  public productSrv = inject(ProductService);
  protected commonSrv = inject(CommonService);
  translateService = inject(TranslateConfigService);
  private fidelitySrv = inject(FidelityProgramService);
  private location = inject(Location);
  private route = inject(Router)

  isFrench: boolean = this.translateService.currentLang === Language.French;
  totalPoints: number;

  constructor(
  ) {
    this.storageService.getUserConnected();
    this.user = this.qrcodeSrv.currenUser;
  }

  async ngOnInit(): Promise<void> {
    this.isLoading = true;
    this.cart = {
      ...JSON.parse(this.storageService.load('cart')),
      items: JSON.parse(this.storageService.load('items')),
    };


    const userEntity = await this.fidelitySrv.getPoints({});
    this.totalPoints = this.fidelitySrv.calculateTotalPointsOrder(this.cart?.items, userEntity);

    this.supplier = JSON.parse(this.storageService.load('supplier'));

    this.isLoading = false;
  }

  async ionViewWillEnter(): Promise<void> {
  }

  async validatePoint(): Promise<void> {
    try {
      this.isLoading = true;

      // Safely load user data with error handling
      let connectUser: User | null = null;
      const userInfoString = this.storageService.load('USER_INFO');

      if (userInfoString) {
        try {
          connectUser = JSON.parse(userInfoString) as User;
        } catch (error) {
          console.error('Error parsing USER_INFO:', error);
        }
      }

      // If connectUser is null or undefined, use a default/fallback
      if (!connectUser) {
        // You might want to handle this case better based on your application logic
        this.isLoading = false;
        this.commonSrv?.showToast?.({
          color: 'danger',
          message: 'User information not found'
        });
        return;
      }

      // Determine the correct user based on category
      const user = connectUser?.category === UserCategory.Commercial ? this.user : connectUser;

      // Ensure supplier exists before proceeding
      if (!this.supplier) {
        this.isLoading = false;
        this.commonSrv?.showToast?.({
          color: 'danger',
          message: 'Supplier information not found'
        });
        return;
      }

      // Ensure cart exists
      if (!this.cart) {
        this.isLoading = false;
        this.commonSrv?.showToast?.({
          color: 'danger',
          message: 'Cart information not found'
        });
        return;
      }

      // Create order object with proper error handling
      const order: OrderSupplier = {
        user,
        supplier: this.supplier,
        cart: {
          ...this.cart,
        },
        qrCodeData: JSON.parse(this.storageService.load('qrCodeData')),
      };

      // Make API call with error handling
      let response;
      try {
        response = await this.scannerSrv.validateScanData(order);
      } catch (error) {
        console.error('Error validating scan data:', error);
        this.isLoading = false;
        this.commonSrv?.showToast?.({
          color: 'danger',
          message: 'Error validating scan data'
        });
        return;
      }

      this.isLoading = false;

      if (!(response instanceof HttpErrorResponse)) {
        // Clear storage data safely
        try {
          this.storageService.remove('items');
          this.storageService.remove('cart');
          this.storageService.remove('suppliers');
          this.storageService.remove('qrCodeData');
          
          this.productSrv.dataQrCode = [];
          this.productSrv.currentDataProductScan = [];
        } catch (error) {
          console.error('Error removing storage items:', error);
        }

        // Navigate based on user category
        if ([UserCategory.Commercial, UserCategory.DonutAnimator].includes(connectUser?.category)) {
          this.qrcodeSrv.currenUser = null;
          this.user = null;
          this.route.navigate(['order/history/list-order'])
            .catch(err => console.error('Navigation error:', err));
        } else {
          this.route.navigate(['navigation/fidelity-program'])
            .catch(err => console.error('Navigation error:', err));
        }
      } else {
        // Handle error response
        const errorMessage = response?.error?.message || 'Operation failed';
        this.commonSrv?.showToast?.({
          color: 'danger',
          message: errorMessage
        });
      }
    } catch (error) {
      // Global error handler
      console.error('Unexpected error in validatePoint:', error);
      this.isLoading = false;
      this.commonSrv?.showToast?.({
        color: 'danger',
        message: 'An unexpected error occurred'
      });
    } finally {
      // Ensure isLoading is reset no matter what happens
      this.isLoading = false;
    }
  }
  back() {
    this.location.back()
    // this.router.navigate(['list-others-btp']);
  }

  getCategory(code: number): string {
    switch (code) {
      case ParticularCategory.BHB:
        return 'BHB';
      case ParticularCategory.BS:
        return 'BS';
      case ParticularCategory.BPI:
        return 'BPI';
      default:
        return 'Unknown Category';
    }
  }
}
