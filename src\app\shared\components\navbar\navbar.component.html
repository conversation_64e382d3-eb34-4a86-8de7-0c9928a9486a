<ion-tabs>
  <ion-tab-bar slot="bottom" *ngIf="commonService?.user?._id && commonService?.showNav">
    <!-- Page d'accueil - accessible à tous -->
    <ion-tab-button class="tab-home">
      <div [ngClass]="{'active': [ 'navigation/home', 'navigation/home-alt' ].includes(commonService.tab) }"
        (click)="commonService.tab = ([userCategory.DonutAnimator, userCategory.Particular].includes(commonService.user?.category)   ?  'navigation/home-alt' : 'navigation/home'); setActiveTab(commonService.tab)">
        <ion-img src="/assets/icons/home.svg"></ion-img>
      </div>
      <ion-label class="label-tab">{{"tab-bar.home" | translate}}</ion-label>
    </ion-tab-button>

    <!-- Achats - accessible aux clients et commerciaux -->
    <ion-tab-button class="tab-purchases" *ngIf="commonService.user?.category !== userCategory.DonutAnimator"
      (click)="commonService.tab = ( commonService.user?.category === userCategory.Particular ? 'order/product-scan' : 'order/new/first-step'); setActiveTab(commonService.tab)">

      <div [ngClass]="{ active: commonService.tab === 'order/particular-order/method-order' }">
        <ion-img src="/assets/icons/Buy.svg"></ion-img>
      </div>

      <ion-label class="label-tab">{{ commonService.user.category === userCategory.Particular ? ('tab-bar.scan' | translate ): 'tab-bar.purchases' | translate }}</ion-label>
    </ion-tab-button>


    <!-- <ion-tab-button (click)="commonService.tab = '/order/validate-order'; setActiveTab(commonService.tab)"
      class="tab-account" *ngIf="commonService.user?.category == userCategory.Commercial">
      <div [ngClass]="{'active': commonService.tab === '/order/validate-order'}">
        <ion-img src="/assets/icons/history deux.svg"></ion-img>
    </div>
      <ion-label class="label-tab">{{"tab-bar.indirect-command" | translate}}</ion-label>
    </ion-tab-button> -->

    <!-- Points - accessible au particuliers -->
    <ion-tab-button *ngIf="commonService.user?.category == userCategory.Particular" class="tab-account">
      <div [ngClass]="{'active': commonService.tab === 'navigation/fidelity-program'}"
        (click)=" commonService.tab = 'navigation/fidelity-program'; setActiveTab(commonService.tab)">
        <ion-img src="/assets/icons/points-menu.svg"></ion-img>
      </div>
      <ion-label class="label-tab">{{"tab-bar.points" | translate}}</ion-label>
    </ion-tab-button>


    <!-- Comptes et portefeuille - accessible aux clients -->
    <ion-tab-button *ngIf="![userCategory.Commercial, userCategory.DonutAnimator, userCategory.Particular].includes(commonService.user?.category)   
    && commonService.user?.authorizations.includes(balanceAction.VIEW)" class="tab-account">
      <div [ngClass]="{'active': commonService.tab === 'navigation/account-balance'}"
        (click)=" commonService.tab = 'navigation/account-balance'; setActiveTab(commonService.tab)"> <ion-img
          src="/assets/icons/Wallet.svg"> </ion-img> </div> <ion-label class="label-tab">{{"tab-bar.account"
        |translate}}</ion-label>
    </ion-tab-button>

    <!-- Market place - accessible aux clients -->
    <ion-tab-button
      *ngIf="[userCategory.Particular, userCategory.DonutAnimator].includes(commonService.user?.category) || commonService.user.authorizations.includes(itemAction.VIEW)"
      (click)="commonService.tab = 'navigation/market-place'; setActiveTab(commonService.tab)">
      <div [ngClass]="{'active':commonService.tab ===  'navigation/market-place'}">
        <ion-img src="/assets/icons/Bag.svg"></ion-img>
      </div>
      <ion-label class="label-tab">{{ ('tab-bar.marketPlace') | translate }}</ion-label>
    </ion-tab-button>


    <!-- Historique des commandes - accessible aux commerciaux -->
    <ion-tab-button (click)="commonService.tab = '/order/history/list-order'; setActiveTab(commonService.tab)"
      class="tab-account"
      *ngIf="[userCategory.DonutAnimator].includes(commonService.user?.category)">
      <div [ngClass]="{'active': commonService.tab === '/order/history/list-order'}">
        <ion-img src="/assets/icons/history deux.svg"></ion-img>
      </div>
      <ion-label class="label-tab">{{"tab-bar.scan" | translate}}</ion-label>
    </ion-tab-button>

    <!-- Historique des commandes - accessible aux client direct -->
    <ion-tab-button (click)="commonService.tab = '/order/history'; setActiveTab(commonService.tab)" class="tab-account"
      *ngIf="[userCategory.Commercial, userCategory.CompanyUser].includes(commonService.user?.category) ">
      <div [ngClass]="{'active': commonService.tab === '/order/history'}">
        <ion-img src="/assets/icons/history deux.svg"></ion-img>
      </div>
      <ion-label class="label-tab">{{"tab-bar.order" | translate}}</ion-label>
    </ion-tab-button>

    <ion-tab-button *ngIf="[userCategory.Commercial, userCategory.DonutAnimator].includes(commonService.user?.category)"
      class="tab-account">
      <div
        [ngClass]="{'active': ['navigation/manage-user/user-list', '/navigation/indirect-user'].includes(commonService.tab )}"
        (click)="commonService.tab = ( commonService.user?.category === userCategory.DonutAnimator ? '/navigation/indirect-user' : 'navigation/manage-user/user-list'); ; setActiveTab(commonService.tab)">
        <ion-img src="/assets/icons/clt-company.svg"></ion-img>
      </div>
      <ion-label class="label-tab"> Clients </ion-label>
    </ion-tab-button>

    <!-- Historique des commandes revendeur - accessible aux commerciaux -->
    <!-- <ion-tab-button (click)="commonService.tab = '/order/order-reseller/history'; setActiveTab(commonService.tab)"
      class="tab-account" *ngIf="commonService.user?.category == userCategory.Commercial">
      <div [ngClass]="{'active': commonService.tab === '/order/order-reseller/history'}">
        <ion-img src="/assets/icons/icon-cmd-rdv.svg"></ion-img>
      </div>
      <ion-label class="label-tab">{{"tab-bar.order-retail" | translate}}</ion-label>
    </ion-tab-button> -->

    <!-- Menu Autres - accessible à tous -->
    <ion-tab-button class="tab-orthers">
      <div (click)="openModal()">
        <ion-img src="/assets/icons/Category.svg"></ion-img>
      </div>
      <ion-label class="label-tab">{{"tab-bar.other" | translate}}</ion-label>
    </ion-tab-button>
  </ion-tab-bar>
</ion-tabs>