import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ToastController } from '@ionic/angular';
import { lastValueFrom } from 'rxjs';
import { CartItem, CartItemQRCode } from 'src/app/shared/models/cart.model';
import { Price } from 'src/app/shared/models/price';
import { Product } from 'src/app/shared/models/product.model';
import { QrCodeData } from 'src/app/shared/models/qr-code';
import { ToastModel } from 'src/app/shared/models/toast.model';
import { Unit } from 'src/app/shared/models/unit.model';
import { BaseUrlService } from 'src/app/shared/services/base-url.service';
import { CommonService } from 'src/app/shared/services/common.service';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ProductService {
  unit: Unit;
  prices: Price[] = [];
  url: string;

  currentDataProductScan: CartItemQRCode[] = []
  dataQrCode: Partial<QrCodeData>[] = []

  constructor(
    private http: HttpClient,
    private commonSrv: CommonService,
    private baseUrlService: BaseUrlService,
    public toastController: ToastController,
  ) {
    this.url = this.baseUrlService.getOrigin() + environment.basePath + 'products';
  }

  async getProducts(param?: any): Promise<{ count: number; data: Product[] }> {
    try {
      let params = new HttpParams();
      if (param?.limit) { params = params.append('limit', param?.limit); }

      return await lastValueFrom(this.http.get<{ count: number; data: Product[] }>(this.url, { params }));
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error)
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      }
      await this.commonSrv.showToast(toastMessage);

      return error;
    }
  }

  async getProduct(id: string): Promise<Product> {
    try {
      return await lastValueFrom(this.http.get<Product>(`${this.url}/${id}`));
    } catch (error) {
      const errorMessage = this.commonSrv.getError('', error)
      const toastMessage: ToastModel = {
        message: errorMessage.message,
        color: 'danger',
      }
      await this.commonSrv.showToast(toastMessage);

      return error;
    }
  }
}
