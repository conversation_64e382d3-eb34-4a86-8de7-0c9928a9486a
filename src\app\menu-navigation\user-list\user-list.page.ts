import { Component, inject, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { Language } from 'src/app/shared/enum/language.enum';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { CompanyAction, UserAction } from 'src/app/shared/models/authorization.action';


@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.page.html',
  styleUrls: ['./user-list.page.scss'],
})
export class UserListPage implements OnInit {
  private location = inject(Location);

  translateService = inject(TranslateConfigService);
  translateSrv = inject( TranslateService)

  clients = [];

  companyAction = CompanyAction;
  userAction = UserAction;

  constructor() { }

  ngOnInit() {
    this.updateLanguage();
  }

  updateLanguage(){
    const isFrench = this.translateService.currentLang === Language.French;
    this.clients = [
      {
        title: isFrench ? "Clients directs" : "Directs clients",
        link: "/navigation/companies-account"
      },
      {
        title: isFrench ? "Clients indirects" : "Indirects clients",
        link: "/navigation/indirect-user"
      }
    ]
  }


  back() {
    this.location.back()
    // this.router.navigate(['list-others-btp']);
  }
}
