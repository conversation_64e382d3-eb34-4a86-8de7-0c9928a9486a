  @import "src/theme/mixins.scss";

  ion-header {
    .header {
      --padding-start: var(--space-5);
      --padding-end: var(--space-5);
      --padding-top: var(--space-5);
      --border-color: transparent;
      --background-color: transparent;
      background: $color-fouth;
      @include v-align;

      ion-img {
        width: $padding-4;
        margin-right: calc(25 * var(--res));
      }

      .title {
        font-family: $font-regular;
        text-align: start;
        color: var(--clr-primary-900);
      }
    }
  }

  ion-content {
    app-purchase-summary {
      margin: 1rem 0;
    }

    .order-history-page {
      background-color: #e5e5e5;
      // height: 100%;
      text-align: -webkit-center;
      width: 100%;
      @include v-align;
      align-items: baseline;

      ion-label {
        display: block;
        margin-bottom: 0.4em;
      }

      .historic-bill-detail-container {
        background: var(--clr-default-400);
        background: $color-nine;
        border-radius: 1rem;
        width: 100%;
        padding-bottom: 0.2rem;
        margin-top: 10px;
        box-sizing: content-box;

        .order-detail {
          width: 91%;
          padding: 0 0.5em;
          background: $color-nine;
          margin: 1rem calc(41* var(--res));
          border-radius: 5px;
          border: 1px solid $color-twelve;
          box-shadow: lightgrey 0px 4px 16px;

          .h3-title {
            margin: 2vh 0;
            color: $color-primary;
            ;
          }

          .bill-info {
            display: flex;
            justify-content: center;
            align-items: center;

            .right-block,
            .left-block {
              width: 50%;
              letter-spacing: 1px;
              text-align: initial;
            }

            .left-block {
              display: flex;
              align-items: flex-end;
              flex-direction: column;
            }

            .right-block .title {
              margin-bottom: 0.5em;
              color: rgba(0, 0, 0, 0.87);
            }

            .left-block .value {
              white-space: nowrap;
              display: flex;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.87);
              margin-bottom: 0.5em;
            }
          }
        }

        .btn-validate {
          width: 95%;

          ion-button {
            margin-top: 0.5em;

            .green-btn {
              color: white;
            }
          }
        }

        .flex-dir {
          display: flex;
          justify-content: space-between;
          width: 95%;

          ion-button {
            width: 48%;
          }
        }

        .list-products {
          width: 95%;
          padding: 0 0.5em;
          background: $color-nine;
          margin-bottom: 1rem !important;
          border-radius: 5px;
          // border: 1px solid $color-twelve;
          // box-shadow: lightgrey 0px 4px 16px;

          .h3-title {
            margin: 2vh 0;
            color: $color-primary;
          }

          .title {
            font-family: $font-demiBold;
            font-weight: 600;
            color: $color-primary;
          }

          .form--header {
            text-transform: unset;
          }

          .list-elt-contain {
            box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.09);
            border-radius: 1px;
            overflow-x: hidden;
            overflow-y: auto;
            max-height: calc(25 * var(--resH));
          }

          .list-elt-header,
          .list-elt-contain {
            padding: 5px 16px;
          }

          .list-elt-header,
          .list-elt-contain .list-elt {
            font-size: 14px;
            display: flex;
            justify-content: center;
            align-items: center;

            .col {
              height: 100%;
              padding: 5px 0px;
              display: flex;
              text-align: initial;
            }

            .col.col-desc {
              width: calc(100% - 300px);

              @media only screen and (max-width: 530px) {
                width: 80px;
              }
            }

            .col.col-qdt {
              width: 80px;
            }

            .col.col-price {
              width: 105px;
              justify-content: center;
            }

            .col.col-amount {
              width: 105px;
              justify-content: flex-end;
            }

            .col.col-desc .col-desc-elt {
              height: 100%;
              display: flex;
              justify-content: ce;

              .col-desc-elt-contain {
                width: 25px;
                height: 100%;
              }

              .label {
                margin-left: 10px;
              }
            }
          }

          .list-elt-header {
            height: 30px;

            .col {
              color: rgba(0, 0, 0, 0.65);
              height: 100%;
              text-align: center;
            }

            .col.col-desc {
              text-align: unset;
            }
          }

          .list-elt-contain>div {
            border-bottom: 1px solid rgba(0, 0, 0, 0.24);
          }

          .list-elt-contain>div:last-child {
            border-bottom: none;
          }
        }

        .order-summary {
          width: 91%;
          padding: 0 0.5em;
          background: $color-nine;
          margin: 0 calc(41* var(--res)) 1rem;
          border-radius: 5px;
          border: 1px solid $color-twelve;
          box-shadow: lightgrey 0px 4px 16px;

          .h3-title {
            margin: 2vh 0;
            color: $color-primary;

          }

          .bill-info {
            display: flex;
            justify-content: center;
            align-items: center;

            .right-block,
            .left-block {
              width: 50%;
              letter-spacing: 1px;
              text-align: initial;
            }

            .left-block {
              display: flex;
              align-items: flex-end;
              flex-direction: column;
            }

            .right-block .title {
              margin-bottom: 0.5em;
              color: rgba(0, 0, 0, 0.87);
            }

            .left-block .value {
              white-space: nowrap;
              display: flex;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.87);
              margin-bottom: 0.5em;
            }

            .reason {
              margin-bottom: 10px;
            }
          }
        }

        .info-deliver {
          width: 91%;
          padding: 0 0.5em;
          background: $color-nine;
          margin: 1rem calc(41* var(--res));
          border-radius: 5px;
          border: 1px solid $color-twelve;
          box-shadow: lightgrey 0px 4px 16px;
          padding: 10px;

          .h3-title {
            margin: 2vh 0;
            color: $color-primary;

          }

          .bill-info {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 15px;

            .right-block,
            .left-block {
              width: 50%;
              letter-spacing: 1px;
              text-align: initial;
            }

            .left-block {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
            }

            .right-block .title {
              margin-bottom: 0.5em;
              color: rgba(0, 0, 0, 0.87);
              font-size: 59%;
            }

            .right-block .left-block .value {
              white-space: nowrap;
              display: flex;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.87);
              margin-bottom: 0.5em;

            }

          }

          .button-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            width: 100%;
            margin-top: 15px;
          }
        }

        .title {
          font-family: $font-demiBold;
          font-weight: 600;
        }

        .slide-container {
          margin-top: $margin-4;
          margin-bottom: $margin-4;
          flex: 1;

          ion-slide {
            ion-card {
              height: 100%;
              width: 100%;
              padding: $padding-small;

              @include v-h-align;

              ion-card-content {
                @include v-h-align;

                .mWidth {
                  width: 100%;
                }
              }
            }
          }
        }

        .product-container {
          ion-slide {
            height: calc(8.3 * var(--resH));
            width: 100px !important;

            ion-card {
              padding: 0;
              height: 100%;
              box-shadow: none;

              ion-card-content {
                height: 100%;
                display: flex;
                justify-content: space-between;
                gap: 2px;

                .detail {
                  width: 100%;
                  height: 100%;
                  @include align-column-around;

                  ion-label {
                    font-size: calc(29 * var(--res));
                  }

                  .sac {
                    height: 100%;
                  }
                }

                ion-img {
                  width: 55%;
                }

                ion-label {
                  color: black;
                }
              }
            }
          }
        }

        .form--footer {
          display: flex;
          justify-content: center;
          align-items: center;
        }

      }
    }

    .padding-horizontal {
      padding: 0 $padding-5;

      ion-label {
        display: block;
      }

      ion-text {
        font-size: calc(40.7 * var(--res));
      }
    }

    ul {
      // height: 40%;
      overflow-y: auto;
      margin-bottom: 0.5em;

      .list-elt {
        gap: 0.2em;
        @include v-center;
        align-items: flex-start;
        margin: 0 $padding-5;
        padding: calc(20 * var(--res)) $padding-5;

        .col {
          color: #3c597d;
          @include truncate-text;
          font-size: calc(35 * var(--res));
          font-family: $font-demiBold;

          &:first-child {
            width: 41%;

            ion-label:first-child {
              @include truncate-text;
              font-family: $font-demiBold;
              font-size: calc(35 * var(--res));
            }
          }

          &:nth-child(2) {
            width: 23%;
            min-width: 27px;
          }

          &:last-child {
            flex-grow: 1;
            text-align: end;
          }

          &.product {
            gap: 5px;
            flex-wrap: wrap;
            @include v-center;
            justify-content: flex-start;

            :last-child {
              @include v-align;
              font-size: calc(29.1 * var(--res));
            }

            :first-child {
              width: 100%;
              text-align: start;
            }
          }

          @media only screen and (max-width: 530px) {
            &.product {
              gap: 0;
            }
          }
        }

        .equal {
          width: 32% !important;
        }

        &.line {
          padding: 0;
          margin: 0 calc(2 * $padding-5);
          border-bottom: #ccdef1 solid 1px;
        }

        &.head {
          background-color: #f1f8ff;
          border-radius: calc(12 * var(--res));

          .col {
            color: #000;
          }
        }

        &.footer {
          background-color: #f5f6f6;
          border-radius: calc(12 * var(--res));

          ion-label {
            color: $color-fiftheen;
            font-family: $font-bold;
          }
        }
      }

      .border {
        margin-top: $padding-5;

        li {
          @include v-h-between;
          padding-bottom: $padding-small;

          &:first-child {
            ion-text {
              &:first-child {
                color: #303950;
              }

              &:last-child {
                color: #103a5a;
              }
            }
          }

          &:last-child ion-text {
            color: $color-secondary;
          }
        }
      }
    }
  }