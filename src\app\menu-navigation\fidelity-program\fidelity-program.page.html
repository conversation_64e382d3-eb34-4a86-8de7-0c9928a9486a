<ion-header>
  <ion-toolbar class="header">
    <ion-img (click)="back()" slot="start" src="/assets/icons/arrow-blue.svg"></ion-img>
    <ion-title class="title">{{ "fidelity-page.title" | translate | truncateString:15 }}</ion-title>
    <ion-button slot="end" class="addrefferal-btn" (click)="openReferralBottomSheet()">{{ "fidelity-page.add-referral" |
      translate }}</ion-button>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div id="container">
    <!-- <ion-tab-bar slot="top" class="tab-container ion-no-border">
      <ion-tab-button (click)="currentTab = 'points'; loadContent()" [ngClass]="{'active': currentTab === 'points'}">
        <ion-title>{{"fidelity-page.my-points" | translate}}</ion-title>
      </ion-tab-button>

      <ion-tab-button (click)="currentTab = 'gifts'; loadContent()" [ngClass]="{'active': currentTab === 'gifts'}">
        <ion-title>{{"fidelity-page.available-Gifts" | translate}}</ion-title>
      </ion-tab-button>

      <ion-tab-button (click)="currentTab = 'list'; loadContent()"
        [ngClass]="{'active': currentTab === 'list', 'hidden-tab': true}">
        <ion-title>{{"fidelity-page.referral-list" | translate}}</ion-title>
      </ion-tab-button>
    </ion-tab-bar> -->

    <div class="container">
      <div *ngIf="currentTab === 'points'">
        <!-- Ghost Loading for Points Card -->
        <div class="points-card" *ngIf="!isLoading; else pointsGhostLoading"
          [ngStyle]="{'background-image': 'url(' + imagesUrl[(points?.status ?? 1) - 1]+')'}">
          <div class="points-info">
            <h2>{{ "fidelity-page.points" | translate }}</h2>
            <div class="points-value" [ngClass]="(points?.status ?? 1) | loyaltyProgramClassColor">
              {{ (points?.validate || '0') | number}} {{"fidelity-page.pts" | translate}}
            </div>
            <div class="pending-points">
              {{"fidelity-page.waiting" | translate}}
              <span>{{ (points?.unValidated || '0') | number }} Pts</span>
            </div>
            <div class="category-info">
              <div class="category-name">{{ (points?.status ?? 1) | loyaltyProgramLevelLabel }}</div>
              <div class="category-points">{{ (currentAdvantages?.pointsRange?.min ?? 0) + ' - ' +
                (currentAdvantages?.pointsRange?.max ?? 2000)}}</div>
            </div>
          </div>
        </div>
        <ng-template #pointsGhostLoading>
          <div class="ghost-loading points-card"></div>
        </ng-template>

        <div class="section-title">{{"fidelity-page.description-title" | translate}}</div>

        <ion-list *ngIf="!isLoading; else advantagesGhostLoading">
          <ion-item *ngFor="let advantage of getCurrentAdvantages(currentAdvantages)">
            <img class="colombe-checkbox" [src]="imagesCheckBox[(points?.status ?? 1) - 1]">
            <ion-label>{{ advantage }}</ion-label>
          </ion-item>
        </ion-list>
        <ng-template #advantagesGhostLoading>
          <div class="ghost-loading advantage-item" *ngFor="let i of [1,2,3,4]"></div>
        </ng-template>

        <div class="section-title">{{"fidelity-page.category" | translate}}</div>

        <div class="categories-grid">
          <div class="loyalty-card ghost-loading" *ngIf="isLoading"></div>

          <div class="loyalty-card" *ngFor="let advantage of advantages; let i = index"
            [ngStyle]="{'background-color': bgColors[advantage?.statusValue - 1]}">
            <div class="card-header"
              [ngStyle]="{'background-image': 'url(' + imagesUrl[advantage?.statusValue - 1]+ ')'}">
              <div class="elt-card">
                <div class="profile-label">Profil</div>
                <h2>{{ advantage.label }}</h2>
                <div class="points-label">
                  {{ advantage.pointsRange.max ? (advantage?.pointsRange?.min + ' - ' + advantage?.pointsRange?.max + '
                  Points') : (advantage?.pointsRange?.min + '+ Points') }}
                </div>
              </div>
            </div>

            <div class="card-content">
              <div class="benefits-list">
                <div class="benefit-item" *ngFor="let benefit of getCurrentAdvantages(advantage)">
                  <img class="colombe-checkbox" [src]="imagesCheckBox[advantage?.statusValue - 1]">
                  <span>{{ benefit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div *ngIf="currentTab === 'gifts'">
        <div class="gifts-empty-state">
          <ion-img class="full-page-image" src="/assets/icons/Research paper-amico.svg"></ion-img>
          <ion-label class="no-gifts-label">{{ "fidelity-page.no-gifts" | translate }}</ion-label>
        </div>
      </div>
    </div>
  </div>
</ion-content>

<ng-template #cardGhostLoading>
  <div class="ghost-loading loyalty-card"></div>
</ng-template>