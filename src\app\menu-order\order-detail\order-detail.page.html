<ion-header>
  <ion-toolbar class="header">
    <ion-img (click)="back()" slot="start" src="/assets/icons/arrow-back.svg"></ion-img>
    <ion-title class="title">
      {{ "order.detail.reference" | translate | capitalize}}
      <span class="color-primary">
        {{ orderService?.order?.customerReference || orderService?.order?.appReference || marketService?.order?.appReference }}
      </span>
    </ion-title>
    <ion-buttons slot="end"
      *ngIf="![userCategory.Commercial, userCategory?.DonutAnimator, userCategory.Particular].includes(commonSrv?.user?.category)">
      <ion-button (click)="presentPopover($event)">
        <ion-icon name="ellipsis-vertical"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div id="container"
    *ngIf="[userCategory.Commercial, userCategory.CompanyUser, userCategory?.DonutAnimator].includes(commonSrv?.user?.category)"
    class="order-history-page">
    <div class="dialogRef" class="scroller-container historic-bill-detail-container containers">
      <div class="order-detail">
        <ion-title class="h3-title ">{{ "order.detail.title" | translate | capitalize}}</ion-title>
        <div class="bill-info">
          <div class="right-block">
            <ion-label class="title">{{ "order.detail.reference" | translate | capitalize}} : </ion-label>
            <ion-label class="title">{{ "order.detail.created-at" | translate | capitalize}} :</ion-label>
            <ion-label class="title">{{ "order.detail.name" | translate | capitalize}} :</ion-label>
            <ion-label class="title">{{ "order.detail.email" | translate | capitalize}} :</ion-label>
            <ion-label class="title" *ngIf="orderService?.order?.dates?.validated">
              {{ "order.detail.validated-at" | translate | capitalize}} :</ion-label>
            <ion-label class="title" *ngIf="orderService?.order?.reference">
              {{ "history-page.ref" | translate | capitalize}} :</ion-label>
          </div>
          <div class="left-block">
            <ion-label class="value">{{orderService?.order?.appReference}}</ion-label>
            <ion-label class="value">{{orderService?.order?.created_at | date: 'dd/MM/YY'}} &nbsp;
              <span class="value">{{("preposition.to" | translate | capitalize) + (orderService?.order?.created_at |
                date:
                'HH:mm:ss')}}</span>
            </ion-label>
            <ion-label class="value">{{orderService?.order?.user?.firstName || '' | capitalize}} {{
              orderService?.order?.user?.lastName ||
              'N/A' | capitalize}}</ion-label>
            <ion-label class="value">{{orderService?.order?.user?.email || 'N/A' | truncateString: 18 | capitalize
              }}</ion-label>
            <ion-label *ngIf="orderService?.order?.dates?.validated" class="value">
              {{orderService?.order?.dates?.validated| date: 'dd/MM/YY'}}
            </ion-label>
            <ion-label *ngIf="orderService?.order?.reference" class="value">
              {{orderService?.order?.reference | capitalize}}
            </ion-label>
          </div>
        </div>
      </div>
      <div *ngIf="false">
        <ion-title class="h3-title ">{{ "order.detail.product-list" | translate}}</ion-title>
        <div class=" list-elt-header ">
          <div class="col col-desc "></div>
          <div class="col col-qdt title">Quantité</div>
          <div class="col col-price title">PU</div>
          <div class="col col-amount title">Montant</div>
        </div>
        <div class="list-elt-contain">
          <div class=" list-elt " *ngFor="let item of orderService?.order?.cart?.items">
            <div class="col col-desc ">
              <div class="col-desc-elt">
                <ion-img src="../../../assets/images/cimencam.png"></ion-img>
              </div>
            </div>
            <div class="col col-qdt ">
              {{item?.quantity}}
              {{item?.packaging?.label}}
            </div>
            <div class="col col-price ">
              {{item.unitPrice | number:''}} XAF
            </div>
            <div class="col col-amount ">
              {{(item?.unitPrice) * item?.quantity | number:'' }}
              XAF</div>
          </div>
        </div>
      </div>

      <app-purchase-summary [cart]="orderService?.order?.cart" [orderPrice]="orderService?.order?.cart?.amount"
        [shipping]="orderService?.order?.cart?.shipping" [itemsLimited]="orderService?.order?.cart?.items?.slice(0, 3)"
        [shippingInfo]="orderService?.order?.cart?.amount?.shippingInfo"
        [carrierOrderInformation]="orderService?.order?.carrier">
      </app-purchase-summary>

      <div class="order-summary" *ngIf="orderService?.order?.rejectReason">
        <ion-title class="h3-title">{{ "order.detail.motif-rejet" | translate}} </ion-title>
        <div class="bill-info">
          <ion-label class="value reason">{{orderService?.order?.rejectReason}}</ion-label>
        </div>
      </div>
      <div class="info-deliver">
        <!-- <div class="bill-info" *ngIf="!editMode">
          <div class="right-block">
            <ion-label class="title">{{ "order-new-page.first-step.driver-name" | translate | capitalize}}
              :</ion-label>
            <ion-label class="title">{{ "bottom-sheet-validation.tel" | translate | capitalize}} :</ion-label>
            <ion-label class="title">{{ "order-new-page.first-step.driver-id" | translate | capitalize}}
              :</ion-label>
            <ion-label class="title">{{ "order-new-page.first-step.driver-category" | translate | capitalize}}
              :</ion-label>
            <ion-label class="title">{{ "order-new-page.first-step.driver-vehicle" | translate | capitalize}}
              :</ion-label>
              <ion-label class="title">{{ "order-new-page.first-step.driver-license" | translate | capitalize}}
                :</ion-label>
          </div>
          <div class="left-block">
            <ion-label class="value">{{orderService?.order?.carrier?.name || 'N/A' }}</ion-label>
            <ion-label class="value">{{orderService?.order?.carrier?.phone || 'N/A' }}</ion-label>
            <ion-label class="value">{{orderService?.order?.carrier?.idCard || 'N/A' }}</ion-label>
            <ion-label class="value">{{orderService?.order?.carrier?.vehicleCategory || 'N/A' }}</ion-label>
            <ion-label class="value">{{orderService?.order?.carrier?.vehiclePlate || 'N/A' }}</ion-label>
            <ion-label class="value">{{orderService?.order?.carrier?.driverLicense || 'N/A' }}</ion-label>

          </div>
        </div> -->

        <form [formGroup]="carrierForm" *ngIf="editMode">
          <ion-title class="h3-title">{{ "order-new-page.third-step.carrier-title" | translate }}</ion-title>

          <ion-item>
            <ion-label position="floating" class="title">{{ "order-new-page.first-step.driver-name" | translate |
              capitalize}}</ion-label>
            <ion-input formControlName="name" class="value"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating" class="title">{{ "bottom-sheet-validation.tel" | translate |
              capitalize}}</ion-label>
            <ion-input formControlName="phone" class="value"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating" class="title">{{ "order-new-page.first-step.driver-id" | translate |
              capitalize}}</ion-label>
            <ion-input formControlName="idCard" class="value"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating" class="title">{{ "order-new-page.first-step.driver-category" | translate |
              capitalize}}</ion-label>
            <ion-input formControlName="vehicleCategory" class="value"></ion-input>
          </ion-item>

          <ion-item>
            <ion-label position="floating" class="title">{{ "order-new-page.first-step.driver-vehicle" | translate |
              capitalize}}</ion-label>
            <ion-input formControlName="vehiclePlate" class="value"></ion-input>
          </ion-item>

          <div class="button-container">
            <ion-button expand="full" color="danger" (click)="toggleEditMode()">{{ "button.cancel" | translate |
              capitalize}}</ion-button>
            <ion-button expand="full" color="primary" (click)="saveCarrier()">{{"profile.retailer.save-button-label" |
              translate|
              capitalize}}</ion-button>
          </div>
        </form>

        <ion-button expand="full" class="btn-class" *ngIf="!editMode" (click)="toggleEditMode()">
          {{ "button.save-edit-carrier" | translate }}
        </ion-button>
      </div>
      <div class="info-deliver">
        <ion-title class="h3-title">{{ "order.detail.delivery.title" | translate}}</ion-title>
        <div class="bill-info">
          <div class="right-block">
            <ion-label class="title">{{ "order.detail.delivery.mode" | translate | capitalize}} : </ion-label>
            <ion-label class="title">{{ "order.detail.delivery.location" | translate | capitalize}} : </ion-label>
            <ion-label class="title">{{ "order.detail.delivery.date" | translate | capitalize}} : </ion-label>
            <ion-label class="title">{{ "order.detail.delivery.amount" | translate | capitalize}} : </ion-label>
          </div>
          <div class="left-block">
            <ion-label class="value">{{orderService?.order?.cart?.renderType | transformEnumToString: 'renderType' }}
            </ion-label>
            <ion-label class="value">{{orderService?.order?.cart?.renderType == 1 ?
              orderService?.order?.cart?.store?.label :
              orderService?.order?.cart?.shipping?.label || 'N/A' }}</ion-label>
            <ion-label class="value">
              {{(orderService?.order?.cart?.shipping.deliveryDate | date : 'dd/MM/yyyy': 'fr') || 'N/A'}}
            </ion-label>
            <ion-label class="value">{{orderService?.order?.cart?.amount?.shipping | number:''}} XAF</ion-label>
          </div>
        </div>
      </div>

      <div class="flex-dir">
        <!--validate btn for DRH-->
        <ion-button class="btn--meduim btn--upper" color="primary" (click)="showModalConfirmValidation()"
          *ngIf="!isValidate && [orderStatus?.CREDIT_IN_VALIDATION].includes(orderService?.order?.status)
          && commonSrv?.user?.employeeType === employeeType.DRH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> VALIDER</ion-label>
        </ion-button>

        <!--validate btn for coordo-RH-->
        <ion-button class="btn--meduim btn--upper" color="primary" (click)="showModalConfirmValidation()"
          *ngIf="!isValidate && [orderStatus.CREATED].includes(orderService?.order?.status)
           && commonSrv?.user?.employeeType === employeeType.CORDO_RH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> VALIDER</ion-label>
        </ion-button>

        <!--reject btn for DRH-->
        <ion-button class="btn--meduim btn--upper" color="danger" (click)="showModalRejectOrder()"
          *ngIf="!isReject && [orderStatus?.CREDIT_IN_VALIDATION].includes(orderService?.order?.status)
          && commonSrv?.user?.employeeType === employeeType.DRH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> REJETER</ion-label>
        </ion-button>

        <!--reject btn for coordo-RH-->
        <ion-button class="btn--meduim btn--upper" color="danger" (click)="showModalRejectOrder()"
          *ngIf="!isReject && [orderStatus?.CREATED].includes(orderService?.order?.status)
          && commonSrv?.user?.employeeType === employeeType.CORDO_RH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> REJETER</ion-label>
        </ion-button>
      </div>
      <!-- <div class="btn-validate"
        *ngIf="orderService?.order?.status != orderStatus.VALIDATED && commonSrv.user.category !== userCategory.Commercial">
        <ion-button class="btn--meduim btn--upper" color="primary" expand="block" routerLink="order-update"> -->
      <!-- <ion-spinner name="bubbles" *ngIf="isLoading"></ion-spinner> -->
      <!-- <ion-label *ngIf="!isLoading" class="green-btn"> MODIFIER LA COMMANDE </ion-label>
        </ion-button>
      </div> -->
      <div class="btn-validate" *ngIf="orderService?.order?.status === orderStatus.CREDIT_IN_AWAIT_VALIDATION 
        && commonSrv.user.category === userCategory.Commercial" (click)="showModalConfirmValidation()">
        <ion-button class="btn--meduim btn--upper" color="primary" expand="block">
          <ion-label *ngIf="!isLoading" class="green-btn"> {{'VALIDER LA COMMANDE'}} </ion-label>
        </ion-button>
      </div>
      <!-- <div class="btn-validate" *ngIf="commonSrv.user.category !== userCategory.CompanyUser">
        <ion-button class="btn--meduim btn--upper" color="medium" expand="block"
          (click)="generatePurchaseOrder(orderService?.order?._id)">
          <ion-spinner name="bubbles" *ngIf="isLoading"></ion-spinner>
          <ion-label *ngIf="!isLoading" class="green-btn"> {{"reseller-new-page.detail.download-order" | translate}}
          </ion-label>
        </ion-button>
      </div> -->
    </div>
  </div>
  <div id="container" *ngIf="commonSrv.user.category === userCategory.Particular && !commonSrv.orderDetailNotification"
    class="order-history-page">
    <div class="dialogRef" class="scroller-container historic-bill-detail-container containers">
      <div class="order-detail">
        <ion-title class="h3-title ">{{ "order.detail.title" | translate | capitalize}}</ion-title>
        <div class="bill-info">
          <div class="right-block">
            <ion-label class="title">{{ "order.detail.reference" | translate | capitalize}} : </ion-label>
            <ion-label class="title">{{ "order.detail.created-at" | translate | capitalize}} :</ion-label>
            <ion-label class="title">{{ "order.detail.name" | translate | capitalize}} :</ion-label>
            <ion-label class="title">{{ "order.detail.email" | translate | capitalize}} :</ion-label>
            <ion-label class="title" *ngIf="orderRetailService?.orderRetail?.dates?.validated">
              {{ "order.detail.validated-at" | translate | capitalize}} :</ion-label>
            <ion-label class="title" *ngIf="orderRetailService?.orderRetail?.reference">
              {{ "history-page.ref" | translate | capitalize}} :</ion-label>
          </div>
          <div class="left-block">
            <ion-label class="value">{{orderRetailService?.orderRetail?.appReference}}</ion-label>
            <ion-label class="value">{{orderRetailService?.orderRetail?.created_at | date: 'dd/MM/YY'}} &nbsp;
              <span class="value">{{("preposition.to" | translate | capitalize) +
                (orderRetailService?.orderRetail?.created_at |
                date:
                'HH:mm:ss')}}</span>
            </ion-label>
            <ion-label class="value">{{orderRetailService?.orderRetail?.user?.firstName || 'N/A' | capitalize}}
            </ion-label>
            <ion-label class="value">{{orderRetailService?.orderRetail?.user?.email || 'N/A' | truncateString: 18 |
              capitalize
              }}</ion-label>
            <ion-label *ngIf="orderRetailService?.orderRetail?.dates?.validated" class="value">
              {{orderRetailService?.orderRetail?.dates?.validated| date: 'dd/MM/YY'}}
            </ion-label>
            <ion-label *ngIf="orderRetailService?.orderRetail?.reference" class="value">
              {{orderRetailService?.orderRetail?.reference | capitalize}}
            </ion-label>
          </div>
        </div>
      </div>
      <div *ngIf="false">
        <ion-title class="h3-title ">{{ "order.detail.product-list" | translate}}</ion-title>
        <div class=" list-elt-header ">
          <div class="col col-desc "></div>
          <div class="col col-qdt title">Quantité</div>
          <div class="col col-price title">PU</div>
          <div class="col col-amount title">Montant</div>
        </div>
        <div class="list-elt-contain">
          <div class=" list-elt " *ngFor="let item of orderRetailService?.orderRetail?.cart?.items">
            <div class="col col-desc ">
              <div class="col-desc-elt">
                <ion-img src="{{item?.product?.image}}"></ion-img>
              </div>
            </div>
            <div class="col col-qdt ">
              {{item?.quantity}}
              {{item?.packaging?.label}}
            </div>
            <div class="col col-price ">
              {{item.unitPrice | number:'00'}} XAF
            </div>
            <div class="col col-amount ">
              {{(item?.unitPrice) * item?.quantity | number:'00' }}
              XAF</div>
          </div>
        </div>
      </div>

      <app-purchase-summary [cart]="orderRetailService?.orderRetail?.cart"
        [orderPrice]="orderRetailService?.orderRetail?.cart?.items?.unitPrice"
        [shipping]="orderRetailService?.orderRetail?.cart?.items"
        [itemsLimited]="orderRetailService?.orderRetail?.cart?.items?.slice(0, 3)"
        [shippingInfo]="orderRetailService?.orderRetail?.cart?.items?.unitPrice"
        [carrierOrderInformation]="orderService?.order?.carrier">
      </app-purchase-summary>

      <div class="order-summary" *ngIf="orderRetailService?.orderRetail?.validation?.raison">
        <ion-title class="h3-title">{{ "order.detail.motif-rejet" | translate}} </ion-title>
        <div class="bill-info">
          <ion-label class="value reason">{{orderRetailService?.orderRetail?.validation?.raison}}</ion-label>
        </div>
      </div>

      <!-- <div class="info-deliver">
        <ion-title class="h3-title">{{ "order.detail.delivery.title" | translate}}</ion-title>
        <div class="bill-info">
          <div class="right-block">
            <ion-label class="title">{{ "order.detail.delivery.mode" | translate | capitalize}} : </ion-label>
            <ion-label class="title">{{ "order.detail.delivery.location" | translate | capitalize}} : </ion-label>
            <ion-label class="title">{{ "order.detail.delivery.date" | translate | capitalize}} : </ion-label>
            <ion-label class="title">{{ "order.detail.delivery.amount" | translate | capitalize}} : </ion-label>
          </div>
          <div class="left-block">
            <ion-label class="value">{{orderRetailService?.orderRetail?.cart?.renderType | transformEnumToString: 'renderType' }}
            </ion-label>
            <ion-label class="value">{{orderRetailService?.orderRetail?.cart?.renderType == 1 ?
              orderRetailService?.orderRetail?.cart?.items?.product?.label :
              orderRetailService?.orderRetail?.cart?.items?.product?.label || 'N/A' }}</ion-label>
            <ion-label class="value">
              {{(orderRetailService?.orderRetail?.created_at | date : 'dd/MM/yyyy': 'fr') || 'N/A'}}
            </ion-label>
            <ion-label class="value">{{orderRetailService?.orderRetail?.cart?.amount?.shipping || 00 | number:''}} XAF</ion-label>
          </div>
        </div>

      </div> -->
      <div class="flex-dir">
        <!--validate btn for DRH-->
        <ion-button class="btn--meduim btn--upper" color="primary" (click)="showModalConfirmValidation()"
          *ngIf="!isValidate && [orderStatus?.CREDIT_IN_VALIDATION].includes(orderRetailService?.orderRetail?.status)
          && commonSrv?.user?.employeeType === employeeType.DRH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> VALIDER</ion-label>
        </ion-button>

        <!--validate btn for coordo-RH-->
        <ion-button class="btn--meduim btn--upper" color="primary" (click)="showModalConfirmValidation()"
          *ngIf="!isValidate && [orderStatus.CREATED].includes(orderRetailService?.orderRetail?.status)
           && commonSrv?.user?.employeeType === employeeType.CORDO_RH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> VALIDER</ion-label>
        </ion-button>

        <!--reject btn for DRH-->
        <ion-button class="btn--meduim btn--upper" color="danger" (click)="showModalRejectOrder()"
          *ngIf="!isReject && [orderStatus?.CREDIT_IN_VALIDATION].includes(orderRetailService?.orderRetail?.status)
          && commonSrv?.user?.employeeType === employeeType.DRH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> REJETER</ion-label>
        </ion-button>

        <!--reject btn for coordo-RH-->
        <ion-button class="btn--meduim btn--upper" color="danger" (click)="showModalRejectOrder()"
          *ngIf="!isReject && [orderStatus?.CREATED].includes(orderRetailService?.orderRetail?.status)
          && commonSrv?.user?.employeeType === employeeType.CORDO_RH && commonSrv?.user?.authorizations.includes(orderAction.VALIDATE)" expand="block">
          <ion-label> REJETER</ion-label>
        </ion-button>
      </div>
      <!-- <div class="btn-validate"
        *ngIf="orderRetailService?.orderRetail?.status != orderStatus.VALIDATED && commonSrv.user.category !== userCategory.Commercial">
        <ion-button class="btn--meduim btn--upper" color="primary" expand="block" routerLink="order-update"> -->
      <!-- <ion-spinner name="bubbles" *ngIf="isLoading"></ion-spinner> -->
      <!-- <ion-label *ngIf="!isLoading" class="green-btn"> MODIFIER LA COMMANDE </ion-label>
        </ion-button>
      </div> -->
      <!-- <div class="btn-validate" *ngIf="orderRetailService?.orderRetail?.status === orderStatus.CREDIT_IN_AWAIT_VALIDATION 
        && commonSrv.user.category === userCategory.Commercial" (click)="showModalConfirmValidation()">
        <ion-button class="btn--meduim btn--upper" color="primary" expand="block">
          <ion-label *ngIf="!isLoading" class="green-btn"> {{'VALIDER LA COMMANDE'}} </ion-label>
        </ion-button>
      </div> -->
      <!-- <div class="btn-validate" *ngIf="commonSrv.user.category !== userCategory.CompanyUser">
        <ion-button class="btn--meduim btn--upper" color="medium" expand="block"
          (click)="generatePurchaseOrder(orderRetailService?.orderRetail?._id)">
          <ion-spinner name="bubbles" *ngIf="isLoading"></ion-spinner>
          <ion-label *ngIf="!isLoading" class="green-btn"> {{"reseller-new-page.detail.download-order" | translate}}
          </ion-label>
        </ion-button>
      </div> -->
    </div>
  </div>

  <div id="container" *ngIf="commonSrv.orderDetailNotification" class="order-history-page">
    <div class="dialogRef" class="scroller-container historic-bill-detail-container containers">
      <div class="order-detail">
        <ion-title class="h3-title ">{{ "order.detail.title" | translate | capitalize}}</ion-title>
        <div class="bill-info">
          <div class="right-block">
            <ion-label class="title">{{ "order.detail.reference" | translate | capitalize}} : </ion-label>
            <ion-label class="title">{{ "order.detail.created-at" | translate | capitalize}} :</ion-label>
            <ion-label class="title">{{ "order.detail.name" | translate | capitalize}} :</ion-label>
            <ion-label class="title">{{ "order.detail.email" | translate | capitalize}} :</ion-label>
            <ion-label class="title" *ngIf="marketService?.order?.validation?.date">
              {{ "order.detail.validated-at" | translate | capitalize}} :</ion-label>
            <!-- <ion-label class="title" *ngIf="marketService?.order?.reference">
              {{ "history-page.ref" | translate | capitalize}} :</ion-label> -->
          </div>
          <div class="left-block">
            <ion-label class="value">{{ marketService?.order?.appReference }}</ion-label>
            <ion-label class="value">{{ marketService?.order?.created_at | date: 'dd/MM/YY'}} &nbsp;
              <span class="value">{{("preposition.to" | translate | capitalize) +
                (marketService?.order?.created_at |
                date:
                'HH:mm:ss')}}</span>
            </ion-label>
            <ion-label class="value">{{marketService?.order?.user?.firstName || marketService?.order?.user?.lastName || 'N/A' | capitalize}}
            </ion-label>
            <ion-label class="value">{{ marketService?.order?.user?.email || 'N/A' | truncateString: 18 |
              capitalize
              }}</ion-label>
            <ion-label *ngIf="marketService?.order?.validation?.date" class="value">
              {{marketService?.order?.validation?.date | date: 'dd/MM/YY'}}
            </ion-label>
            <ion-label *ngIf="orderRetailService?.orderRetail?.reference " class="value">
              {{orderRetailService?.orderRetail?.reference | capitalize}}
            </ion-label>
          </div>
        </div>
      </div>
      <div *ngIf="false">
        <ion-title class="h3-title ">{{ "order.detail.product-list" | translate}}</ion-title>
        <div class=" list-elt-header ">
          <div class="col col-desc "></div>
          <div class="col col-qdt title">Quantité</div>
          <div class="col col-price title">PU</div>
          <div class="col col-amount title">Montant</div>
        </div>
        <div class="list-elt-contain">
          <div class=" list-elt">
            <div class="col col-desc ">
              <div class="col-desc-elt">
                <ion-img src="{{marketService?.order?.cart?.items?.image}}"></ion-img>
              </div>
            </div>
            <div class="col col-qdt ">
              {{ 1 }}
              {{marketService?.order?.cart?.items?.name}}
            </div>
            <div class="col col-price ">
              {{marketService?.order?.cart?.items.price | number:'00'}} POINTS
            </div>
            <div class="col col-amount ">
              {{(marketService?.order?.cart?.items?.price) * 1 | number:'00' }}
              POINTS</div>
          </div>
        </div>
      </div>

      <ul class="product-list">
        <li class="list-elt head">
          <ion-label class="col"><strong>{{"order-new-page.third-step.product" | translate}}</strong></ion-label>
          <ion-label class="col"><strong>{{"order-new-page.third-step.qte" | translate}}</strong></ion-label>
          <ion-label class="col"><strong>{{"order-new-page.third-step.unitPrice"
              | translate}}</strong></ion-label>
        </li>

        <div *ngIf="marketService?.order?.cart?.items">
          <li class="list-elt">
            <div class="col product">
              <ion-label>{{marketService?.order?.cart?.items?.name | capitalize}}</ion-label>
              <ion-label>{{marketService?.order?.cart?.items?.name}}</ion-label>
            </div>
            <ion-label class="col">{{marketService?.order?.cart?.quantity}}</ion-label>
            <ion-label class="col">{{marketService?.order?.cart?.items?.price |
              number: '':
              'fr'}} POINTS</ion-label>
          </li>
          <li class=" list-elt line">
          </li>
        </div>
      </ul>


      <div class="order-summary" *ngIf="marketService?.order?.validation?.raison">
        <ion-title class="h3-title">{{ "order.detail.motif-rejet" | translate}} </ion-title>
        <div class="bill-info">
          <ion-label class="value reason">{{marketService?.order?.validation?.raison}}</ion-label>
        </div>
      </div>



    </div>
  </div>
</ion-content>