@import "src/theme/mixins.scss";

ion-header {
  .header {
    --padding-start: var(--space-5);
    --padding-end: var(--space-5);
    --padding-top: var(--space-5);
    --border-color: transparent;
    --background-color: transparent;
    background: $color-fouth;
    @include v-align;

    ion-img {
      width: $padding-4;
      margin-right: calc(25 * var(--res));
    }

    .title {
      font-family: $font-regular;
      text-align: start;
      color: var(--clr-primary-900);
    }
  }
}

ion-content {
  #container {
    padding: calc(41 * var(--res));
    padding-top: 0;

    .tab-container {
      margin: auto;
      min-height: 35px;
      gap: 1em;
      margin-bottom: $margin-4;

      ion-tab-button {
        min-width: fit-content;
      }

      ion-title {
        font-size: calc(41 * var(--res));
        color: $color-sixtheen;
        font-family: $font-demiBold;
      }

      border: none;

      .active {
        ion-title {
          color: $color-fiftheen;
        }

        color: $color-sixtheen;
        border-bottom: 3px solid $color-secondary;
      }
    }
  }

  .order-list {
    .order {
      border-bottom: 1px solid #8597ad;
      box-shadow: none;
      border-radius: unset;
      margin-bottom: $margin-5;

      ion-card-content {
        @include v-h-between;
        padding: $padding-small;

        .detail {
          ion-label {
            display: block;
            line-height: initial;
            margin-top: calc(15.7 * var(--res));
            font-family: $font-demiBold;
            color: $color-fiftheen;
            font-size: calc(36 * var(--res));

            .clr-primary-400 {
              color: var(--ion-color-tertiary-contrast) !important;
            }

            .bg-success-200 {
              background-color: var(--ion-color-success-tint) !important;
              padding: 3px 6px;
              border-radius: 4px;
            }

            .clr-info-500 {
              color: var(--ion-color-tertiary-tint) !important;
            }

            .bg-info-100 {
              background-color: #cef !important;
              padding: 2px;
              border-radius: 4px;
            }

            .clr-default-400 {
              color: var(--ion-color-tertiary-contras) !important;
            }

            .clr-danger-400 {
              color: #fff;
            }

            .bg-info-500 {
              background-color: var(--ion-color-tertiary-tint) !important;
              padding: 2px;
              border-radius: 4px;
            }

            .bg-tertiary-200 {
              background-color: var(--ion-color-tertiary-tint) !important;
              padding: 2px;
              border-radius: 4px;
            }

            .bg-danger-100 {
              background-color: hsl(0, 96%, 39%) !important;
              padding: 2px;
              border-radius: 4px;
            }
          }
        }

        .icon {
          ion-icon {
            width: 1.5rem;
            height: 1.5rem;
          }
        }
      }
    }
  }

  .skeleton {
    height: 4em;
    width: 100%;
    margin-bottom: 1rem;

    ion-skeleton-text {
      border-radius: 10px;
      width: 100%;
      height: 100%;
    }
  }

  .empty-list {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    height: 100%;

    ion-img {
      width: 50%;
      padding: 1rem 0;
    }
  }
}
