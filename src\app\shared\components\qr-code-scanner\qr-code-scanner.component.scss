.scanner-container {
  // padding-top: env(safe-area-inset-top);
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  // backdrop-filter: blur(8px);
}

.header {
  padding: 16px;
  color: white;
  display: flex;
  z-index: 100;
  align-items: center;

  h1 {
    margin: 0;
    font-size: 18px;
    z-index: 100;
    margin-left: 8px;
  }

  ion-icon {
    z-index: 100;

  }

  .back-button {
    z-index: 100;
    --color: white;
  }
}

.scanner-view {
  position: relative;
  height: 100%;
  z-index: 100;
  display: flex;
  text-align: center;
  flex-direction: column;
  padding: 5rem 2em;

  .scan-text {
    z-index: 100;
    text-align: center;
    font-size: var(--fs-18-px);
    color: white;
    padding-bottom: 2em;
  }
}

.scan-window {
  aspect-ratio: 1;
  position: relative;
  background: transparent;
  border-radius: 22px;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.6);
  margin-bottom: 2em;
}

.corner-border {
  position: absolute;
  width: 2em;
  height: 2em;
  border: 4px solid white;

  &.top-left {
    top: 0;
    left: 0;
    border-right: 0;
    border-bottom: 0;
    border-top-left-radius: 20px;
  }

  &.top-right {
    top: 0;
    right: 0;
    border-left: 0;
    border-bottom: 0;
    border-top-right-radius: 20px;
  }

  &.bottom-left {
    bottom: 0;
    left: 0;
    border-right: 0;
    border-top: 0;
    border-bottom-left-radius: 20px;
  }

  &.bottom-right {
    bottom: 0;
    right: 0;
    border-left: 0;
    border-top: 0;
    border-bottom-right-radius: 2rem;
  }

  /* Ligne de scan */
  .scanner-window .scan-line {
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #4CAF50;
    top: 50%;
    animation: scan 2s linear infinite;
    /* Animation de la ligne de scan */
  }

  /* Animation de la ligne de scan */
  @keyframes scan {
    0% {
      transform: translateY(-100px);
    }

    50% {
      transform: translateY(100px);
    }

    100% {
      transform: translateY(-100px);
    }
  }
}