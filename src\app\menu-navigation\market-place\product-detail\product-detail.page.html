<ion-header>
  <ion-toolbar>
    <div class="header-page">
      <div class="div-start">
        <div class="logo-tag">
          <ion-img (click)="back()" slot="start" src="/assets/icons/arrow-back.svg"></ion-img>
        </div>
        <ion-title class="title"> {{ product?.name| truncateString }}</ion-title>

      </div>
    </div>
  </ion-toolbar>

</ion-header>

<ion-content [fullscreen]="true">
  <div id="container">
    <div class="item-detail-focus">
      <ion-item>
        <ion-slides [options]="slideOpts">
          <ion-slide>
            <ion-img [src]="product?.image" class="head-img"> </ion-img>
          </ion-slide>
        </ion-slides>
      </ion-item>

      <p>{{product?.description || 'N/A'}} </p>

      <div class="row">
        <div class="detail-item">
          <ion-label class="title"> {{product?.price || 'N/A'}} Point(s) </ion-label>
          <!-- <div class="stars">
            <ion-icon *ngFor="let stars of starsIcon" [src]="stars" class="small-stars"></ion-icon>
            <ion-text class="small-text"> 500 avis </ion-text>
          </div> -->
        </div>
        <!-- <div class="btn-group">
          <ion-button color="danger">
            <ion-img src="/assets/icons/red-heart.png"></ion-img>
          </ion-button>
          <ion-button class="btn-border" color="ligth">
            <ion-img src="/assets/icons/download-grey.svg" class="telec-btn"></ion-img>
          </ion-button>
        </div> -->
      </div>
      <ion-button [disabled]='isOpen' class="btn btn--medium btn--upper pay-btn" color="primary" expand="block" *ngIf="user.category === userCategory.Particular"
        (click)="showModalConfirmValidation()">
        {{"market-place.reclaim" | translate}}
      </ion-button>
    </div>
  </div>
</ion-content>