import { Component } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Platform } from '@ionic/angular';
import { AuthenticationService } from './shared/services/authentication.service';
import { TranslateConfigService } from './shared/services/translate-config.service';
import { filter } from 'rxjs/operators';
import { CommonService } from './shared/services/common.service';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent {
  showNavBar: boolean;

  constructor(
    private platform: Platform,
    private router: Router,
    private authenticationService: AuthenticationService,
    private translateConfigService: TranslateConfigService,
    public commonSrv: CommonService,
  ) {
    this.translateConfigService.getDefaultLanguage();
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.checkRoute(event?.urlAfterRedirects);
    });
    this.initializeApp();
  }

  checkRoute(url: string) {
    const routesToShowNavBar = ['/navigation/account-balance', '/navigation/home', '/navigation/market-place'];
    this.showNavBar = routesToShowNavBar.includes(url);
    // this.commonSrv.showNav = this.showNavBar;
  }

  async initializeApp() {
    const route = this.router?.url;
    if (route === '/authentication' || route === '/navigation') {
      this.showNavBar = false;
    } else {
      this.showNavBar = true;
    }

    this.handleBackButton();

    // Vérifier l'état de l'authentification et naviguer en conséquence
    this.platform.ready().then(() => {
      this.authenticationService.authState.subscribe((state) => {
        if (state) {

          this.router.navigate(['navigation']);
        } else {
          this.router.navigate(['/authentication']);
        }
      });
    });

  }

  handleBackButton() {
    console.log('============== CURRENT URL IS =================:', this.router.url);

    this.platform.backButton.subscribeWithPriority(10, () => {
      // On récupère le path sans les éventuels paramètres ou query
      const currentUrl = this.router.url.split('?')[0].split('#')[0];
      if (currentUrl === '/authentication' || currentUrl === '/navigation' || currentUrl === '/') {
        navigator['app'].exitApp();
      } else {
        // Utilise la navigation Angular pour revenir à la page précédente
        if (window.history.length > 1) {
          window.history.back();
        } else {
          // Si aucun historique, on sort de l'app
          navigator['app'].exitApp();
        }
      }
    });
  }
}
