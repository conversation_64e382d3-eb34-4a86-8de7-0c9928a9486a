<ion-header [translucent]="true">
  <div class="header">
    <ion-img slot="start" (click)="back()" src="/assets/icons/arrow-back.svg"></ion-img>
    <ion-title> Liste des utilisateurs </ion-title>
</div>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="container">
      <a [routerLink]="[client.link]" class="item" *ngFor="let client of clients" [ngStyle]="{'display': client.isAuthorized ? 'block' : 'none'}">
        <ion-img src="/assets/images/man.png"></ion-img>
        <ion-label>
            {{ client.title }}
        </ion-label>
        <ion-icon slot="end" name="chevron-forward"></ion-icon>
      </a>
  </div>
</ion-content>
