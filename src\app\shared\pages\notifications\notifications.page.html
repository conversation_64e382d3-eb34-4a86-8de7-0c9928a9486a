<ion-header>
  <ion-toolbar class="header">
    <ng-container *ngIf="!isSelectionMode">
      <ion-img (click)="back()" slot="start" src="/assets/icons/arrow-blue.svg"></ion-img>
      <ion-title class="title">{{ "notification.your-notification" | translate}}</ion-title>
      <ion-button slot="end" fill="clear" (click)="toggleSelectionMode()" *ngIf="dataNotification?.length > 0">
        <ion-icon name="ellipsis-vertical"></ion-icon>
      </ion-button>
    </ng-container>

    <ng-container *ngIf="isSelectionMode">
      <ion-checkbox slot="start" [checked]="selectedCount === dataNotification.length"
        (ionChange)="selectAllNotifications()">
      </ion-checkbox>
      <ion-title class="selection-title">{{ selectedCount }} {{ "notification.selected" | translate}}</ion-title>
      <ion-button slot="end" fill="clear" color="danger" (click)="deleteSelectedNotifications()"
        [disabled]="isDeletingLoading || selectedCount === 0">
        <ion-icon name="trash"></ion-icon>
      </ion-button>
      <ion-button slot="end" fill="clear" (click)="toggleSelectionMode()">
        <ion-icon name="close"></ion-icon>
      </ion-button>
    </ng-container>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-progress-bar type="indeterminate" *ngIf="isLoading"></ion-progress-bar>
  <div class="ion-text-center ion-margin-top" *ngIf="isDeletingLoading">
    <ion-spinner name="bubbles"></ion-spinner>
  </div>
  <div class="card" [class.content-disabled]="isDeletingLoading">
    <div class="notification-detail" *ngFor="let notification of dataNotification"
      [ngStyle]="notifications(notification.status)"
      [class.selected]="selectedNotifications.has(notification?._id?.toString())"
      (click)="!isSelectionMode ? showDetailNotification(notification) : null">

      <ng-container *ngIf="isSelectionMode">
        <ion-checkbox slot="start" [checked]="selectedNotifications.has(notification?._id?.toString())"
          (ionChange)="toggleNotificationSelection(notification?._id?.toString())" (click)="$event.stopPropagation()">
        </ion-checkbox>
      </ng-container>

      <img src="assets/logos/cadyst.png" alt="Image absente" class="empty-list-img" />
      <div class="detail">
        <div class="message-contain">
          <span>La Cadyst Grain le {{notification?.dates?.created | date:'dd/MM/yy HH:mm:ss '}}</span>
          <div>{{notification?.message | notificationMessageCleaner | truncateString:15}}</div>
        </div>
      </div>
      <div class="status" [ngStyle]="manageStatusNotification(notification.status)"></div>
    </div>

    <div class="empty" *ngIf="!dataNotification?.length && !isLoading">
      <div class="img-container">
        <img src="/assets/icons/Research paper-amico.svg" alt="Image absente" class="empty-list-img" />
      </div>
      <p class="illustration-label">{{ "notification.notification-not-found" | translate}}</p>
    </div>
  </div>
</ion-content>