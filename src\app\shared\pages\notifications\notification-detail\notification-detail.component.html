
<ion-header>
  <ion-toolbar class="header">
    <ion-img (click)="back()" slot="start" src="/assets/icons/arrow-back.svg"></ion-img>
    <ion-title class="title">Détail de la notification.</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content>
  <div class="card">
    <div class="details">
      <div class="form-group">
        <h2>La Cadyst Grain le {{notificationService?.currentNotification?.dates.created | date:'dd/MM/yyyy HH:mm:ss'}}
        </h2>
  
      </div>
  
      <div class="form-group">
        <label class="label" for="categoryInput">Réponse de La Cadyst Grain</label>
        <div nbInput fullWidth size="large" rows="8" cols="40" required class="response">
          {{notificationService.currentNotification?.message | cleanNotificationMessage}}
        </div>
      </div>
  
  
      <div class="form-group">
        <label class="label" for="categoryInput">Categorie de la réclamation</label>
        <input nbInput fullWidth size="large" type="text"
          placeholder="{{notificationService.currentNotification?.feedback?.category.label}}" for="categoryInput" disabled
          class="input">
      </div>
  
      <div class="form-group">
        <label class="label" for="subCategoryInput">Motif de la réclamation</label>
        <input nbInput fullWidth size="large" type="text"
          placeholder="{{notificationService.currentNotification?.feedback?.subCategory.label}}" for="subCategoryInput"
          disabled class="input">
      </div>
  
      <div class="form-group">
        <label class="label" for="messageInput">Contenu de votre réclamation </label>
        <textarea nbInput fullWidth size="large" rows="8" cols="40" required id="messageInput" [disabled]="true"
          class="input" value="{{notificationService.currentNotification?.feedback?.message || 'non renseigné'}}">
              </textarea>
      </div>
      <div class="form-group">
        <label class="label" for="messageInput">La réclamation a été faite le
          {{notificationService.currentNotification?.feedback?.created_at | date:'dd/MM/yyyy HH:mm:ss'}}</label>
      </div>
  
    </div>
  </div>

</ion-content>
