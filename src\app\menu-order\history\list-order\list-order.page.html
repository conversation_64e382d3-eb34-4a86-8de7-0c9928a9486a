<ion-header>
  <ion-toolbar class="header">
    <button (click)="back()" slot="start" class="back-button">
      <ion-img src="/assets/icons/arrow-back.svg"></ion-img>
    </button> <ion-title class="title">{{"list-order.title" | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="container">
    <a [routerLink]="['/order/history']" class="item" 
    *ngIf="commonSrv.user.category !== userCategory.DonutAnimator ||  commonSrv.user.authorizations.includes(orderAction.VIEW)">
      <ion-img [src]="'/assets/images/entreprise.png'" class="order-icon"></ion-img>
      <ion-label>
        {{"list-order.direct" | translate }}
      </ion-label>
      <ion-icon slot="end" name="chevron-forward"></ion-icon>
    </a>
    <a [routerLink]="['/order/validate-client-order-via-qrcode']" class="item"
     *ngIf="commonSrv.user.authorizations.includes(qrCodeAction.VIEW)">
      <ion-img [src]="'/assets/images/qr-scanner.png'" class="order-icon-qrcode"></ion-img>
      <ion-label class="title-qr-code">
        Validate Order
      </ion-label>
      <ion-icon slot="end" name="chevron-forward"></ion-icon>
    </a>

    <a [routerLink]="['/order/validate-order']" class="item"
      *ngIf="commonSrv.user.category === userCategory.DonutAnimator || commonSrv.user.authorizations.includes(supplierAction.VIEW)">
      <ion-img [src]="'/assets/images/man.png'" class="order-icon"></ion-img>
      <ion-label>
        {{"list-order.indirect" | translate }}
      </ion-label>
      <ion-icon slot="end" name="chevron-forward"></ion-icon>
    </a>

  </div>
</ion-content>