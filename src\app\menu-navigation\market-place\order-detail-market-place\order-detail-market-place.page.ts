import { BottomSheetValidationOrderComponent } from 'src/app/shared/components/bottom-sheet-validation-order/bottom-sheet-validation-order.component';
import { ModalProductDetailComponent } from 'src/app/menu-order/order-detail/modal-product-detail/modal-product-detail.component';
import { PopoverComponentComponent } from 'src/app/shared/components/popover-component/popover-component.component';
import { BaseModalComponent } from 'src/app/shared/components/base-modal/base-modal.component';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { MarketPlaceService } from 'src/app/menu-order/services/market-place.service';
import { Alert<PERSON>ontroller, ModalController, PopoverController } from '@ionic/angular';
import { EmployeeType, UserCategory } from 'src/app/shared/enum/user-category.enum';
import { Order, OrderAction, OrderStatus } from 'src/app/shared/models/order';
import { CommonService } from 'src/app/shared/services/common.service';
import { OrderMarket } from 'src/app/shared/models/order-market-place';
import { Language } from 'src/app/shared/enum/language.enum';
import { CartItem } from 'src/app/shared/models/cart.model';
import { ActivatedRoute, Router } from '@angular/router';
import { Component, inject } from '@angular/core';
import { Location } from '@angular/common';

@Component({
  selector: 'app-order-detail',
  templateUrl: './order-detail-market-place.page.html',
  styleUrls: ['./order-detail-market-place.page.scss'],
})
export class OrderDetailMarketPlacePage {
  slideProductOpts = {
    initialSlide: 0,
    speed: 400,
    slidesPerView: 4,
    spaceBetween: 10,
  };

  orders: OrderMarket[];
  isValidate = false;
  isReject = false;
  isLoading = false;
  isPopoverOpen = false;
  order: OrderMarket;


  orderStatus = OrderStatus;
  userCategory = UserCategory;
  orderAction = OrderAction;
  employeeType = EmployeeType;


  private actionHandlers = {
    modifier: () => this.modifyOrder(),
    telecharger: () => this.generatePurchaseOrder(this.marketService?.order?._id),
    annuler: () => this.handleCancellation(),
  };

  private location = inject(Location);
  public marketService = inject(MarketPlaceService);
  private alertController = inject(AlertController);
  public popoverCtrl = inject(PopoverController);
  public commonSrv = inject(CommonService);
  private modalCtrl = inject(ModalController);
  private translateService = inject(TranslateConfigService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);

  async ionViewWillEnter(): Promise<void> {
    if (!this.marketService.order && !this.route.snapshot.params['idOrder']) return this.back();

    if (!this.marketService.order) {
      const id = this.route.snapshot.params['idOrder'];
      this.marketService.order = await this.marketService.find(id);
    }
    this.order = this.marketService.order;
    console.log(this.order);
    
  }

  trackByFn = (index: any, item: any): any => index;

  back(): void {
    this.marketService.order = null;
    this.location.back();
  }

  async presentPopover(ev: Event): Promise<void> {
    const popover = await this.popoverCtrl.create({
      component: PopoverComponentComponent,
      event: ev,
      cssClass: 'custom-popover',
      mode: 'md',
      componentProps: {
        actions: [
          { 
            label: this.getTranslatedText('Modifier la commande', 'Edit order'), 
            action: 'modifier' 
          },
          { 
            label: this.getTranslatedText('Télécharger Bon de co..', 'Download PO'), 
            action: 'telecharger' 
          },
          { 
            label: this.getTranslatedText('Demande d\'annulation', 'Cancellation request'), 
            action: 'annuler' 
          },
        ]
      }
    });
    await popover.present();
  
    const { data } = await popover.onWillDismiss();
    if (!data) return;
    this.handlePopoverAction(data);
  }

  handlePopoverAction(action: string): void {
    const handler = this.actionHandlers[action];
    if (!handler) return;
    handler();
  }

  modifyOrder(): void {
    if (!this.canModifyOrder()) {
      this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? `Une erreur est survenue.`
          : `An error occurred.`,
        color: 'warning'
      });
      return;
    }
    this.navigateToOrderUpdate();
  }


  private canModifyOrder(): boolean {
    return this.marketService?.order?.status !== OrderStatus.VALIDATED &&
      this.commonSrv.user.category !== UserCategory.Commercial;
  }

  private navigateToOrderUpdate(): void {
    const orderId = this.marketService?.order?._id;
    this.router.navigate([`order/detail/${orderId}/order-update`])
      .then(success => console.log(success ? 'Navigation réussie' : 'Échec de la navigation'))
      .catch(err => console.error('Erreur lors de la navigation', err));
  }

  private cancelOrder(): void {
    this.commonSrv.showToast({
      message: this.translateService.currentLang === Language.French
        ? `Une erreur est survenue.`
        : `An error occurred.`,
      color: 'warning'
    });
  }

  async showModalConfirmValidation(): Promise<void> {
    const validOrder = await this.modalCtrl.create({
      component: BottomSheetValidationOrderComponent,
      cssClass: 'modal',
      initialBreakpoint: 0.4,
      breakpoints: [0, 0.75, 0.5],
      mode: 'ios',
      componentProps: {
        statusToUpdate: OrderStatus.PAID,
        order: this.marketService?.order
      },
    });
    await validOrder.present();

    const { data } = await validOrder.onWillDismiss();

    if (data) {
      this.updateOrderStatus(data);
    }
  }

  private updateOrderStatus(data: { status: OrderStatus; reference: string }): void {
    // this.marketService.order.status = data.status;
    // this.marketService.order.appReference = data.appReference;
  }

  async showModalRejectOrder(): Promise<void> {
    const alert = await this.alertController.create({
      header: this.getTranslatedText('Rejet de commande', 'Reject Order'),
      message: this.getTranslatedText(
        'Vous êtes sur le point de rejeter cette commande.\n Confirmez vous cette action ?',
        'You are about to reject this order.\n Do you confirm this action ?'
      ),
      cssClass: 'custom-loading',
      buttons: [
        {
          text: this.getTranslatedText('Annuler', 'Cancel'),
          cssClass: 'alert-button-cancel',
        },
        {
          text: this.getTranslatedText('Rejeter', 'Reject'),
          cssClass: 'alert-button-confirm',
          handler: () => this.rejectOrder(),
        },
      ],
    });
    await alert.present();
  }

  private async rejectOrder(): Promise<void> {
    // await this.marketService.RhRejectOrder(this.marketService.order);
    // this.isReject = true;
    // this.isValidate = true;
  }

  async showDetail(item: CartItem): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: ModalProductDetailComponent,
      cssClass: 'modal',
      initialBreakpoint: 0.35,
      breakpoints: [0, 0.75, 0.8, 0.35, 0.9, 0.95, 1],
      mode: 'ios',
      componentProps: { item, packaging: item?.packaging },
    });
    await modal.present();
  }

  async generatePurchaseOrder(id: string): Promise<void> {
    if (!id) {
      console.log('ID de commande non disponible');
      return;
    }

    this.isLoading = true;
    // try {
    //   const res = await this.marketService.generatePurchaseOrder(id);
    //   if (res?.pdfPath) {
    //     window.open(res.pdfPath, '_blank');
    //   } else {
    //     console.log('Chemin du PDF non disponible');
    //   }
    // } catch (error) {
    //   console.error('Erreur lors de la génération du bon de commande:', error);
    // } finally {
    //   setTimeout(() => { this.isLoading = false; }, 1000);
    // }
  }

  private getTranslatedText(frText: string, enText: string): string {
    return this.translateService.currentLang === Language.French ? frText : enText;
  }

  async handleCancellation() {
    // Vérifier si nous avons une commande valide
    if (!this.order?._id) {
      await this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? 'La commande n\'est pas correctement chargée'
          : 'Order is not properly loaded',
        color: 'warning'
      });
      return;
    }
  
    let defaultMessage = this.translateService.currentLang === Language.French
      ? 'À la demande du client'
      : 'At the customers request';
  
    const modal = await this.modalCtrl.create({
      component: BaseModalComponent,
      cssClass: 'modalClass',
      componentProps: {
        dataModal: {
          confirmButton: 'Envoyer',
          cancelButton: 'Annuler',
          text: 'Demmande d annulation de la commande',
          message: 'Renseigner le motif de la demande',
          isAnnulation: true,
          handler: (messageFromModal: string) => {
            defaultMessage = messageFromModal || defaultMessage;
            this.processCancellationRequest(defaultMessage);
          }
        }
      }
    });
  
    await modal.present();
  
    const { role } = await modal.onWillDismiss();
    if (role === 'confirm') {
      this.showFinalConfirmation(defaultMessage);
    }
  }

  async processCancellationRequest(message: string) {
    if (!this.order?._id) {
      await this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? 'La commande n\'est pas correctement chargée'
          : 'Order is not properly loaded',
        color: 'warning'
      });
      return;
    }

    // try {
    //   this.isLoading = true;

    //   this.order.messageCancellation = message;
    // } catch (error) {
    //   await this.commonSrv.showToast({
    //     message: this.translateService.currentLang === Language.French
    //       ? 'Une erreur est survenue lors du traitement de votre demande.'
    //       : 'An error occurred while processing your request.',
    //     color: 'danger'
    //   });
    // } finally {
    //   this.isLoading = false;
    // }
  }

  async sendCancellationRequest(message: string) {
    if (!this.order?._id) {
      await this.commonSrv.showToast({
        message: this.translateService.currentLang === Language.French
          ? 'La commande n\'est pas correctement chargée'
          : 'Order is not properly loaded',
        color: 'warning'
      });
      return;
    }

    // try {
    //   this.isLoading = true;

    //   const cancellationData: CancellationRequest = {
    //     messageCancellation: message,
    //     cancellationStatus: CancellationStatus.ISSUE
    //   };

    //   const response = await this.marketService.cancellationOrder(
    //     this.order._id,
    //     cancellationData
    //   );

    //   if (response instanceof HttpErrorResponse) {
    //     await this.commonSrv.showToast({
    //       message: this.translateService.currentLang === Language.French
    //         ? `Une erreur est survenue. ${response?.message}`
    //         : `An error occurred. ${response?.message}`,
    //       color: 'warning'
    //     });
    //     return;
    //   }

    //   if (this.order) {
    //     this.order.messageCancellation = message;
    //     this.order.cancellationStatus = CancellationStatus.ISSUE;
    //   }

    //   await this.commonSrv.showToast({
    //     message: this.translateService.currentLang === Language.French
    //       ? `Demande d'annulation pour la commande ${this.order.appReference} envoyée avec succès`
    //       : `Cancellation request for order ${this.order.appReference} sent successfully`,
    //     color: 'success'
    //   });

    //   this.router.navigateByUrl('order/history');
    // } catch (error) {
    //   await this.commonSrv.showToast({
    //     message: this.translateService.currentLang === Language.French
    //       ? 'Une erreur est survenue lors de l\'envoi de la demande.'
    //       : 'An error occurred while sending the request.',
    //     color: 'danger'
    //   });
    // } finally {
    //   this.isLoading = false;
    // }
  }

  private async showFinalConfirmation(message: string) {
    const confirmModal = await this.modalCtrl.create({
      component: BaseModalComponent,
      cssClass: 'modalClass',
      componentProps: {
        dataModal: {
          confirmButton: 'Confirmer',
          cancelButton: 'Annuler',
          text: 'Êtes-vous sûr de vouloir envoyer cette demande d\'annulation ?',
          handler: () => {
            this.sendCancellationRequest(message);
          }
        }
      }
    });

    await confirmModal.present();
  }
}