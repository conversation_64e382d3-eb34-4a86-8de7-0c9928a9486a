<div class="content" [ngClass]="{ 'active-height': commonSvr?.isEdit}">

  <form [formGroup]="userForm">
    <div class="input-group">
      <div class="form-group" [ngClass]="{ 'active': commonSvr?.isEdit}">
        <div class="icon-label">
          <ion-icon slot="start" src="/assets/icons/ProfileBlue.svg" class="icon-elt"></ion-icon>
          <ion-label>{{"profile.retailer.input-name-label" | translate}}</ion-label>
        </div>
        <div class="field-info">
          <ion-label *ngIf="!commonSvr.isEdit">{{(commonService?.user?.lastName || ' ') + ' ' +
            (commonService?.user?.firstName || 'N/A')
            }}</ion-label>
          <ion-input *ngIf="commonSvr?.isEdit" formControlName="firstName" [readonly]="!commonSvr.isEdit" type="text"
            clearInput></ion-input>
        </div>
      </div>

      <div class="form-group" [ngClass]="{ 'active': commonSvr.isEdit}" *ngIf="commonSvr.isEdit">
        <div class="icon-label">
          <ion-icon slot="start" src="/assets/icons/ProfileBlue.svg" class="icon-elt"></ion-icon>
          <ion-label>{{"profile.retailer.input-firstName-label" | translate}}</ion-label>
        </div>
        <div class="field-info">
          <ion-input formControlName="lastName" [readonly]="!commonSvr?.isEdit" type="text" clearInput></ion-input>
        </div>
      </div>

      <div class="form-group" [ngClass]="{ 'active': commonSvr?.isEdit}"
        *ngIf="!commonSvr?.isEdit || ![userCategory.Particular, userCategory.CompanyUser].includes(commonService.user?.category)">
        <div class="icon-label">
          <ion-icon slot="start" src="/assets/icons/CallingBlue.svg" class="icon-elt"></ion-icon>
          <ion-label>{{"profile.retailer.input-phone-label" | translate}}</ion-label>
        </div>
        <div class="field-info">
          <ion-label *ngIf="!commonSvr?.isEdit">{{userForm.get('tel').value}}</ion-label>
          <ion-input *ngIf="commonSvr?.isEdit" formControlName="tel" [readonly]="!commonSvr?.isEdit" type="number"
            clearInput></ion-input>
        </div>
      </div>

      <div class="form-group" *ngIf="!commonSvr.isEdit">
        <div class="icon-label">
          <ion-icon slot="start" src="/assets/icons/LocationBlue.svg" class="icon-elt"></ion-icon>
          <ion-label>{{"profile.retailer.input-address-label" | translate}}</ion-label>
        </div>
        <div class="field-info">
          <ion-label>
            {{commonService.user?.address?.region || 'N/A'}}{{commonService.user?.address?.city ? ', ' : ''}}
            {{commonService.user?.address?.city || ''}}
            {{commonService.user?.address?.district ? ' - ' + (commonService.user?.address?.district || 'N/A') : ''}}
          </ion-label>
        </div>
      </div>

      <div class="form-group" *ngIf="!commonSvr.isEdit">
        <div class="icon-label">
          <img src="../../../../assets/icons/companie.png" class="icon-elt" alt="Company Icon">
          <ion-label>{{"profile.retailer.input-company-label" | translate}}</ion-label>
        </div>
        <div class="field-info">
          <ion-label>
            {{commonService.user?.company?.name || 'N/A'}}
          </ion-label>
        </div>
      </div>

      <div class="form-group" *ngIf="!commonSvr.isEdit">
        <div class="icon-label">
          <img src="../../../../assets/icons/statues.png" class="icon-elt" alt="Company Icon">
          <ion-label>{{"profile.retailer.nui" | translate}}</ion-label>
        </div>
        <div class="field-info">
          <ion-label>
            {{commonService.user?.nui || 'N/A'}}
          </ion-label>
        </div>
      </div>

      <div class="form-group" [ngClass]="{ 'active': commonSvr.isEdit}" formGroupName="address"
        *ngIf="commonSvr.isEdit">
        <div class="icon-label">
          <ion-icon slot="start" src="/assets/icons/LocationBlue.svg" class="icon-elt"></ion-icon>
          <ion-label>{{"profile.retailer.select-region-label" | translate}}</ion-label>
        </div>
        <div class="field-info">
          <ion-select mode="ios" formControlName="region" interface="action-sheet" #region cancelText="Annuler">
            <ion-select-option *ngFor="let region of commonService.getRegions()" value="{{ region }}">{{ region }}
            </ion-select-option>
          </ion-select>
        </div>
      </div>

      <div class="form-group" [ngClass]="{ 'active': commonSvr?.isEdit}" formGroupName="address"
        *ngIf="commonSvr.isEdit">
        <div class="icon-label">
          <ion-icon slot="start" src="/assets/icons/LocationBlue.svg" class="icon-elt"></ion-icon>
          <ion-label>{{"profile.retailer.select-city-label" | translate}}</ion-label>
        </div>
        <div class="field-info">
          <ion-select mode="ios" formControlName="city" cancelText="Annuler" interface="action-sheet">
            <ion-select-option *ngFor="let city of commonService.getCities(userForm.get('address').get('region').value)"
              value="{{ city }}">{{ city }}
            </ion-select-option>
          </ion-select>
        </div>
      </div>

      <div class="form-group" [ngClass]="{ 'active': commonSvr?.isEdit}" formGroupName="address"
        *ngIf="commonSvr.isEdit">
        <div class="icon-label">
          <ion-icon slot="start" src="/assets/icons/LocationBlue.svg" class="icon-elt"></ion-icon>
          <ion-label>{{"profile.retailer.input-district-label" | translate}}</ion-label>
        </div>
        <div class="field-info">
          <ion-input formControlName="district" clearInput placeholder="Entrer votre quartier"></ion-input>
        </div>
      </div>

      <div class="form-group" [ngClass]="{ 'active': commonSvr?.isEdit}">
        <div class="icon-label">
          <ion-icon slot="start" src="/assets/icons/MessageBlue.svg" class="icon-elt"></ion-icon>
          <ion-label>E-mail</ion-label>
        </div>
        <div class="field-info">
          <ion-label *ngIf="!commonSvr.isEdit">{{userForm.get('email').value}}</ion-label>
          <ion-input formControlName="email" [readonly]="!commonSvr?.isEdit" *ngIf="commonSvr?.isEdit" type="email"
            clearInput></ion-input>
        </div>
      </div>

      <div class="form-group" [ngClass]="{ 'active': commonSvr.isEdit}">
        <div class="icon-label">
          <ion-icon slot="start" src="/assets/icons/ShieldDoneBlue.svg" class="icon-elt"></ion-icon>
          <ion-label>CNI</ion-label>
        </div>
        <div class="field-info">
          <ion-label *ngIf="!commonSvr.isEdit">{{userForm.get('cni').value || 'N/A'}}</ion-label>
          <ion-input formControlName="cni" [readonly]="!commonSvr?.isEdit" *ngIf="commonSvr?.isEdit" type="number"
            clearInput></ion-input>
        </div>
      </div>
      <!-- <div class="form-group" [ngClass]="{ 'active': commonSvr.isEdit}">
        <div class="icon-label">
          <ion-icon slot="start" src="/assets/icons/1-https.svg" class="icon-elt"></ion-icon>
          <ion-label>{{"profile.retailer.input-password-label" | translate}}</ion-label>
        </div>
        <div class="field-info">
          <ion-input placeholder="Password" *ngIf="commonSvr.isEdit" formControlName="password" type="password" clearInput></ion-input>
          <div class="password" *ngIf="!commonSvr.isEdit">
            <ion-text>******</ion-text>
          </div>
        </div>
      </div> -->

      <ion-button (click)="update()" *ngIf="commonService?.isEdit" class="btn mbottom250 btn--meduim btn--upper"
        type="submit" color="primary" expand="block">
        <ion-label> {{"profile.retailer.save-button-label" | translate}} </ion-label>
      </ion-button>
      <ion-button *ngIf="commonService?.isEdit" class="btn btn--meduim btn--upper" type="submit" (click)="cancel()"
        color="tertiary" expand="block">
        <ion-label> {{"profile.cancel" | translate}} </ion-label>
      </ion-button>
    </div>
  </form>



</div>