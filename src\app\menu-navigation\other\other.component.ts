import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AlertController, ModalController } from '@ionic/angular';
import { AuthenticationService } from 'src/app/shared/services/authentication.service';
import { Language } from 'src/app/shared/enum/language.enum';
import { CommonService } from 'src/app/shared/services/common.service';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';
import { OthersService } from 'src/app/shared/services/others.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { ReprintInvoicePage } from '../reprint-invoice/reprint-invoice.page';
import { JdeReportPage } from '../jde-report/jde-report.page';
import { JdeLoadsNotDispatchedPage } from '../jde-loads-not-dispatched/jde-loads-not-dispatched.page';
import { RetrievementAction } from 'src/app/shared/models/retrievement.model';
import { ChangeLanguageComponent } from 'src/app/shared/components/change-language/change-language.component';
import { User } from 'src/app/shared/models/user.models';
import { loyaltyProgramAction } from 'src/app/shared/models/fidelity-program.model';

@Component({
  selector: 'app-other',
  templateUrl: './other.component.html',
  styleUrls: ['./other.component.scss'],
})
export class OtherComponent implements OnInit {

  user: User;

  actionsForFrench: {
    color: string;
    label: string;
    icon: string;
    action: Function;
    isAuthorize: boolean
  }[] = [
      {
        color: 'primary',
        label: 'Mon profil',
        icon: '/assets/icons/other-profile.png',
        isAuthorize: true,
        action: () => {
          this.viewAccount();
        },
      },

      // {
      //   color: 'primary',
      //   label: 'Gestion planifications',
      //   icon: '/assets/icons/truck-white.svg',
      //   isAuthorize: true,
      //   action: () => {
      //    },
      // },
      {
        color: 'primary',
        label: 'Gestion des BLS',
        icon: '/assets/icons/truck-white.svg',
        isAuthorize: this.commonSrv?.user?.authorizations.includes(RetrievementAction.VIEW),
        action: () => {
          this.retrievement();
        },
      },
      {
        color: 'primary',
        label: 'Points',
        icon: '/assets/icons/Group 1429.svg',
        isAuthorize: this.commonSrv?.user?.authorizations.includes(loyaltyProgramAction.VIEW),
        action: () => {
          this.viewPoint();
        },
      },

      // {
      //   color: 'primary',
      //   label: 'Enlèvements (Aes) ',
      //   icon: '/assets/icons/truck-white.svg',
      //   isAuthorize: this.commonSrv?.user?.authorizations.includes(AuthRemovalAction.VIEW),
      //   action: () => {
      //     this.removal();
      //   },
      // },
      // {
      //   color: 'primary',
      //   label: 'Solde du compte',
      //   icon: '/assets/icons/wallet-outline.svg',
      //   isAuthorize: this.commonSrv?.user?.category === UserCategory.CompanyUser,
      //   action: () => {
      //     this.accountBalance()
      //   },
      // },
      //TODO: Review authorization to access to the list companies users
      {
        color: 'primary',
        label: 'Comptes clients',
        icon: '/assets/icons/clt-company.svg',
        isAuthorize: [UserCategory.Commercial, UserCategory.DonutAnimator].includes(this.commonSrv.user?.category),
        action: () => {
          this.viewCompanieAccount()
        },
      },
      // {
      //   color: 'primary',
      //   label: 'Recharge de compte',
      //   icon: '/assets/icons/wallet-outline.svg',
      //   isAuthorize: this.commonSrv?.user?.authorizations.includes(ReloadBalanceAction.CAN_RELOAD),
      //   action: () => {
      //     this.ReloadBalance()
      //   },
      // },
      {
        color: 'primary',
        label: 'Catalogue produit',
        icon: '/assets/icons/other-catalogue.png',
        isAuthorize: true,
        action: () => {
          this.viewProducts();
        },
      },
      {
        color: 'primary',
        label: 'Réclamations',
        icon: '/assets/icons/other-claim.png',
        isAuthorize: true,
        action: () => {
          this.showReclamation();
        },
      },
      // {
      //   color: 'primary',
      //   label: 'Extrait de comptes',
      //   icon: '/assets/icons/file-white.svg',
      //   isAuthorize: this.commonSrv?.user?.authorizations?.includes(MenuOthersAction.ACCOUNT_EXTRACT),
      //   action: () => {
      //     this.openJdeReport();
      //   },
      // },
      // {
      //   color: 'primary',
      //   label: 'Edition des factures',
      //   icon: '/assets/icons/file-white.svg',
      //   isAuthorize: this.commonSrv?.user?.authorizations?.includes(MenuOthersAction.EDIT_BILLS),
      //   action: () => {
      //     this.openReprintInvoice();
      //   },
      // },
      // {
      //   color: 'primary',
      //   label: 'AEs non chargées',
      //   icon: '/assets/icons/file-white.svg',
      //   isAuthorize: this.commonSrv?.user?.authorizations?.includes(MenuOthersAction.LOAD_DISPATCHS),
      //   action: () => {
      //     this.openJdeLoadsNotDispatched()
      //   },
      // },
      {
        color: 'primary',
        label: 'Conditions générales',
        icon: '/assets/icons/other-condition.png',
        isAuthorize: true,
        action: () => {
          this.generalConditions()
        },
      },
      {
        color: 'primary',
        label: 'Reporting',
        icon: '/assets/icons/other-reporting.png',
        isAuthorize: true,
        action: () => {
          this.router.navigate(['navigation/reporting/reporting-stats']);
          this.modalCtrl.dismiss();
        },
      },

      {
        color: 'primary',
        label: 'FAQ',
        icon: '/assets/icons/other-faq.png',
        isAuthorize: true,
        action: () => {
          this.viewFAQ();
        },
      },
      // {
      //   color: 'primary',
      //   label: 'Lexique',
      //   icon: '/assets/icons/other-lexique.png',
      //   isAuthorize: true,
      //   action: () => {
      //     this.viewLexique();
      //   },
      // },

      {
        color: 'primary',
        label: 'Contacts',
        icon: '/assets/icons/other-contact.png',
        isAuthorize: true,
        action: () => {
          this.viewContact();
        },
      },
      {
        color: 'primary',
        label: 'Historique',
        icon: '/assets/icons/other-history.png',
        isAuthorize: this.commonSrv.user.category !== UserCategory.DonutAnimator,
        action: () => {
          this.historyOrder()
        },
      },
      {
        color: 'primary',
        label: 'Langue',
        icon: '/assets/icons/other-language.png',
        isAuthorize: true,
        action: () => {
          this.changeLanguage();
        },
      },
    ];

  actionsForEnglish: {
    color: string;
    label: string;
    icon: string;
    isAuthorize: boolean;
    action: Function;
  }[] = [
      {
        color: 'primary',
        label: 'My profil',
        icon: '/assets/icons/other-profile.png',
        isAuthorize: true,
        action: () => {
          this.viewAccount();
        },
      },
      {
        color: 'primary',
        label: 'Points',
        icon: '/assets/icons/Group 1429.svg',
        isAuthorize: this.commonSrv?.user?.authorizations.includes(loyaltyProgramAction.VIEW),
        action: () => {
          this.viewPoint();
        },
      },


      // {
      //   color: 'primary',
      //   label: 'Planning management',
      //   icon: '/assets/icons/truck-white.svg',
      //   isAuthorize: true,
      //   action: () => {
      //     this.retrievement();
      //   },
      // },
      // {
      //   color: 'primary',
      //   label: 'Planning management',
      //   icon: '/assets/icons/truck-white.svg',
      //   isAuthorize: true,
      //   action: () => {
      //   },
      // },
      // {
      //   color: 'primary',
      //   label: 'Retrievement management',
      //   icon: '/assets/icons/truck-white.svg',
      //   isAuthorize: true,
      //   action: () => {
      //     this.retrievement();
      //   },
      // },
      // {
      //   color: 'primary',
      //   label: 'Removal management (Aes)',
      //   icon: '/assets/icons/truck-white.svg',
      //   isAuthorize: true,
      //   action: () => {
      //     this.removal();
      //   },
      // },
      // {
      //   color: 'primary',
      //   label: 'Account balance',
      //   icon: '/assets/icons/wallet-outline.svg',
      //   isAuthorize: this.commonSrv?.user?.category === UserCategory.CompanyUser,
      //   action: () => {
      //     this.accountBalance()
      //   },
      // },
      {
        color: 'primary',
        label: 'Clients accounts',
        icon: '/assets/icons/clt-company.svg',
        isAuthorize: [UserCategory.Commercial, UserCategory.DonutAnimator].includes(this.commonSrv.user?.category),
        action: () => {
          this.viewCompanieAccount()
        },
      },
      // {
      //   color: 'primary',
      //   label: 'Refill account',
      //   icon: '/assets/icons/wallet-outline.svg',
      //   isAuthorize: this.commonSrv?.user?.category === UserCategory.CompanyUser,
      //   action: () => {
      //     this.ReloadBalance()
      //   },
      // },
      {
        color: 'primary',
        label: 'Product catalogue',
        icon: '/assets/icons/other-catalogue.png',
        isAuthorize: true,
        action: () => {
          this.viewProducts();
        },
      },
      {
        color: 'primary',
        label: 'Make a claim',
        icon: '/assets/icons/other-claim.png',
        isAuthorize: true,
        action: () => {
          this.showReclamation();
        },
      },
      // {
      //   color: 'primary',
      //   label: 'Account extract',
      //   icon: '/assets/icons/file-white.svg',
      //   isAuthorize: this.commonSrv?.user?.authorizations?.includes(MenuOthersAction.ACCOUNT_EXTRACT),
      //   action: () => {
      //     this.openJdeReport()
      //   },
      // },
      // {
      //   color: 'primary',
      //   label: 'Bill edition',
      //   icon: '/assets/icons/file-white.svg',
      //   isAuthorize: this.commonSrv?.user?.authorizations?.includes(MenuOthersAction.EDIT_BILLS),
      //   action: () => {
      //     this.openReprintInvoice()
      //   },
      // },
      // {
      //   color: 'primary',
      //   label: 'Load dispatch',
      //   icon: '/assets/icons/file-white.svg',
      //   isAuthorize: this.commonSrv?.user?.authorizations?.includes(MenuOthersAction.LOAD_DISPATCHS),
      //   action: () => {
      //     this.openJdeLoadsNotDispatched()

      //   },
      // },
      {
        color: 'primary',
        label: 'Terms and conditions',
        icon: '/assets/icons/other-condition.png',
        isAuthorize: true,
        action: () => {
          this.generalConditions()
        },
      },
      {
        color: 'primary',
        label: 'Reporting',
        icon: '/assets/icons/other-reporting.png',
        isAuthorize: true,
        action: () => {
          this.router.navigate(['navigation/reporting/reporting-stats']);
          this.modalCtrl.dismiss();
        },
      },

      {
        color: 'primary',
        label: 'FAQ',
        icon: '/assets/icons/other-faq.png',
        isAuthorize: true,
        action: () => {
          this.viewFAQ();
        },
      },
      // {
      //   color: 'primary',
      //   label: 'Lexique',
      //   icon: '/assets/icons/other-lexique.png',
      //   isAuthorize: true,
      //   action: () => {
      //     this.viewLexique();
      //   },
      // },
      {
        color: 'primary',
        label: 'History',
        icon: '/assets/icons/other-history.png',
        isAuthorize: this.commonSrv.user.category !== UserCategory.DonutAnimator,
        action: () => {
          this.historyOrder()
        },
      },
      {
        color: 'primary',
        label: 'Contacts',
        icon: '/assets/icons/other-contact.png',
        isAuthorize: true,
        action: () => {
          this.viewContact();
        },
      },
      {
        color: 'primary',
        label: 'Language',
        icon: '/assets/icons/other-language.png',
        isAuthorize: true,
        action: () => {
          this.changeLanguage();
        },
      },

    ];

  actions: {
    color: string;
    label: string;
    icon: string;
    action: Function;
    isAuthorize: boolean;
  }[] = [];

  filterData: any;
  constructor(
    private router: Router,
    private translateService: TranslateConfigService,
    private alertController: AlertController,
    private authenticationService: AuthenticationService,
    private modalCtrl: ModalController,
    private commonSrv: CommonService,
    private othersSrv: OthersService,
    private storageSrv: StorageService,
  ) { }

  trackByFn(index: any, item: any): any {
    return index;
  }

  ngOnInit(): void {
    this.actions =
      this.translateService.currentLang === Language.French
        ? this.actionsForFrench : this.actionsForEnglish;
    this.user = this.storageSrv.getUserConnected();
  }

  logout(): void {
    this.authenticationService.logout();
    this.modalCtrl.dismiss();
  }

  historyOrder(): void {
    if (this.user.category === UserCategory.Particular) {
      this.router.navigate(['/order/history-particular']);
      this.modalCtrl.dismiss();
    }
    else {
      this.router.navigate(['/order/history']);
      this.modalCtrl.dismiss();
    }
  }

  retrievement(): void {
    if (this.commonSrv.user?.company || this.commonSrv?.user?.category == UserCategory.Commercial) {
      this.router.navigate(['/navigation/retrievements']);
      this.modalCtrl.dismiss();
    }
  }
  removal(): void {
    if (this.commonSrv.user?.company || this.commonSrv?.user?.category == UserCategory.Commercial) {
      this.router.navigate(['/navigation/removals']);
      this.modalCtrl.dismiss();
    }
  }

  viewPoint(): void {
    if (![UserCategory.Commercial, UserCategory.DonutAnimator].includes(this.commonSrv?.user?.category)) {
      this.router.navigate(['/navigation/fidelity-program']);
      this.modalCtrl.dismiss();
    }
  }
  accountBalance() {
    this.router.navigate(['/navigation/account-balance']);
    this.modalCtrl.dismiss();
  }

  viewCompanieAccount() {
    if (this.commonSrv.user.category === UserCategory.DonutAnimator) {
      this.router.navigate(['/navigation/indirect-user']);
    }
    else {
      this.router.navigate(['navigation/manage-user/user-list']);
    }
    this.modalCtrl.dismiss();
  }

  ReloadBalance() {
    this.router.navigate(['/navigation/reload-balance']);
    this.modalCtrl.dismiss();
  }

  async openJdeReport() {
    const modal = await this.modalCtrl.create({
      component: JdeReportPage,
      initialBreakpoint: 0.6,
      cssClass: 'modal',
      breakpoints: [0, 0.6, 0.7],
      mode: 'ios',
      componentProps: {},
    });
    modal.present();
    this.modalCtrl.dismiss();
  }

  async openJdeLoadsNotDispatched() {
    const modal = await this.modalCtrl.create({
      component: JdeLoadsNotDispatchedPage,
      initialBreakpoint: 0.6,
      cssClass: 'modal',
      breakpoints: [0, 0.6, 0.7],
      mode: 'ios',
      componentProps: {
        filterData: this.filterData,
      },
    });
    modal.present();
    this.modalCtrl.dismiss();
  }

  async openReprintInvoice() {
    const modal = await this.modalCtrl.create({
      component: ReprintInvoicePage,
      initialBreakpoint: 0.6,
      cssClass: 'modal',
      breakpoints: [0, 0.6, 0.7],
      mode: 'ios',
      componentProps: {
        filterData: this.filterData,
      },
    });
    modal.present();
    this.modalCtrl.dismiss();

  }

  generalConditions() {
    this.router.navigate(['/navigation/general-condition']);
    this.modalCtrl.dismiss();
  }

  doReclamation() {
    this.router.navigate(['/navigation/claim-form']);
    this.modalCtrl.dismiss();
  }

  showReclamation() {
    this.router.navigate(['/navigation/feedback']);
    this.modalCtrl.dismiss();
  }

  viewAccount() {
    if (this.user?.associatedCompanies && this.user?.associatedCompanies?.length > 0 && this.user?.category === UserCategory.CompanyUser) {
      this.router.navigate(['/navigation/account-management']);
    } else {
      this.router.navigate(['/navigation/account']);
    }
    this.modalCtrl.dismiss();
  }

  viewContact() {
    this.router.navigate(['/navigation/contact']);
    this.modalCtrl.dismiss();
  }

  viewProducts() {
    this.router.navigate(['/navigation/catalogue']);
    this.modalCtrl.dismiss();
  }

  viewFAQ() {
    this.router.navigate(['/navigation/faq-page']);
    this.modalCtrl.dismiss();
  }


  viewLexique() {
    this.router.navigate(['/navigation/lexique']);
    this.modalCtrl.dismiss();
  }

  async changeLanguage(): Promise<void> {
    const modal = await this.modalCtrl.create({
      component: ChangeLanguageComponent,
      cssClass: 'modalClass',
      componentProps: {
      },
    });

    await modal.present();

    const { role } = await modal.onDidDismiss();
    this.modalCtrl.dismiss();

  }

  closeModal(): void {
    this.modalCtrl.dismiss();
  }

}
