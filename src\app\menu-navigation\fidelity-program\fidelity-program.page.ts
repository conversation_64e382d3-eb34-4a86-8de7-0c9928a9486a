import { Component, inject, Inject, OnInit } from '@angular/core';
import { FidelityProgramService } from 'src/app/shared/services/fidelity-program.service';
import { Advantages, Benefits, Points } from 'src/app/shared/models/fidelity-program.model';
import { ModalController } from '@ionic/angular';
import { StorageService } from 'src/app/shared/services/storage.service';
import { SendSponsorComponent } from 'src/app/shared/components/send-sponsor/send-sponsor.component';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';
import { CommonService } from 'src/app/shared/services/common.service';


@Component({
  selector: 'app-fidelity-program',
  templateUrl: './fidelity-program.page.html',
  styleUrls: ['./fidelity-program.page.scss'],
})
export class FidelityProgramPage implements OnInit {
  isLoading = false;
  currentTab: 'points' | 'gifts' | 'list' = 'points';
  points: Points;
  benefit: Benefits;
  advantages: Advantages[];

  advantagesByCategory = {
    'Privilège': [
      'Ce niveau offre des avantages exclusifs',
      'Des récompenser pour votre fidélité.',
      'Profitez de points convertibles en cadeaux.',
    ],
  };


  imagesUrl = [
    '/assets/images/amigo_banner.png',
    '/assets/images/Colombe.png',
    '/assets/images/PELICAN.png',
  ];

  imagesCheckBox = [
    '/assets/icons/checkbox_blue.png',
    '/assets/icons/checkbox_blue.png',
    '/assets/icons/checkbox_yellow.png',
  ];

  bgColors = [
    'var(--clr-secondary-100)',
    'var(--clr-secondary-100)',
    'var(--clr-premium-50)'
  ]

  referralPhoneNumber: string;
  isReferralSheetOpen: boolean = false
  currentAdvantages: Advantages;
  private commonService = inject(CommonService);

  constructor(
    private fidelityService: FidelityProgramService,
    private modalCtrl: ModalController,
    private storagesSrv: StorageService) { }


  async ngOnInit() {
    this.commonService.showNav = true;
  }

  async ionViewWillEnter() {
    this.isLoading = true;
    await this.getPoints();
    await this.loadAvantages();
    this.isLoading = false;
  }

  async getPoints() {
    const user = this.storagesSrv.getUserConnected();

    let query = {}
    if ('company' in user && user.category === UserCategory.CompanyUser) query['companyId'] = user.company._id;

    const data = await this.fidelityService.getPoints(query);
    this.points = data.points;
    this.currentAdvantages = data.advantage;
  }

  getCurrentAdvantages(advantage: Advantages): string[] {
    let avantages = [
      ...(advantage?.oneTimeBenefit || []),
      ...(advantage?.monthlyBenefit || []),
      ...(advantage?.annualBenefit || [])
    ];

    return avantages?.length ? avantages : this.advantagesByCategory['Privilège'];

  }

  back() {
    this.commonService.navigateTo('navigation/home-alt');
  }

  loadContent() {
    setTimeout(() => {
    }, 1000);
  }


  closeReferralBottomSheet() {
    this.isReferralSheetOpen = false;
  }


  async openReferralBottomSheet() {
    const modal = await this.modalCtrl.create({
      component: SendSponsorComponent,
      initialBreakpoint: 0.50,
      cssClass: 'modal',
      breakpoints: [0, 0.5, 0.50, 0.5, 1],
      mode: 'ios',
      handle: true
    });
    await modal.present();
  }

  async loadAvantages() {
    const response = await this.fidelityService.getBenefitRewards(this.points?.status ? { statusValue: { $ne: this.points?.status } } : {});
    this.advantages = response?.data;

  }
}
