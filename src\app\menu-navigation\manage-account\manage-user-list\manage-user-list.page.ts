import { Component, inject, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { Language } from 'src/app/shared/enum/language.enum';
import { CommonService } from 'src/app/shared/services/common.service';
import { CompanyAction, UserAction } from 'src/app/shared/models/authorization.action';


@Component({
  selector: 'app-manage-user-list',
  templateUrl: './manage-user-list.page.html',
  styleUrls: ['./manage-user-list.page.scss'],
})
export class ManageUserListPage implements OnInit {
  location = inject(Location);
  translateService = inject(TranslateConfigService);
  commonSrv = inject(CommonService);
  clients = [];

  constructor() { }

  ngOnInit() {
    const isFrench = this.translateService.currentLang === Language.French;
    this.clients = [
      {
        title: isFrench ? "Clients directs" : "Directs clients",
        link: "/navigation/companies-account",
        isAuthorized: CompanyAction.VIEW in this.commonSrv.user?.authorizations
      },
      {
        title: isFrench ? "Clients indirects" : "Indirects clients",
        link: "/navigation/indirect-user",
        isAuthorized: UserAction.VIEW in this.commonSrv.user?.authorizations
      }
    ]
  }


  back() {
    this.location.back();
  }

}
