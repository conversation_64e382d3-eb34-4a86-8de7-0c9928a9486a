import { Component, inject, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import { CommonService } from 'src/app/shared/services/common.service';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';
import { OrderAction } from 'src/app/shared/models/order';
import { Router } from '@angular/router';
import { SupplierAction } from 'src/app/shared/models/order-particular';
import { OrderSupplierAction, QrCodeAction } from 'src/app/shared/models/authorization.action';


@Component({
  selector: 'app-list-order',
  templateUrl: './list-order.page.html',
  styleUrls: ['./list-order.page.scss'],
})
export class ListOrderPage implements OnInit {

  userCategory = UserCategory;
  orderAction = OrderAction;
  supplierAction = SupplierAction;
  qrCodeAction = QrCodeAction;

  constructor(
    protected commonSrv: CommonService,
    private router: Router,

  ) { commonSrv.handleBackButton(); }

  ngOnInit() {
    this.commonSrv.showNav = true;
  }

  back() {
    [UserCategory.DonutAnimator, UserCategory.Particular].includes(this.commonSrv.user?.category)
      ? this.router.navigate(['/navigation/home-alt']) : this.router.navigate(['/navigation/home']);
  }
}
