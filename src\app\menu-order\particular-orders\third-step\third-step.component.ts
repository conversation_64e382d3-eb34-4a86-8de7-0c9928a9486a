import { PaiementValidationComponent } from 'src/app/menu-order/new-order/paiement-validation/paiement-validation.component';
import { PaiementOnCreditComponent } from 'src/app/menu-order/new-order/paiement-on-credit/paiement-on-credit.component';
import { TranslateConfigService } from 'src/app/shared/services/translate-config.service';
import { Cart, CartItem, OrderPrice, ShippingInfo } from 'src/app/shared/models/cart.model';
import { ProductService } from 'src/app/menu-order/services/product.service';
import { PromoCodeAction } from 'src/app/shared/models/promo-code.model';
import { StorageService } from 'src/app/shared/services/storage.service';
import { OrderService } from 'src/app/menu-order/services/order.service';
import { CommonService, meansOfPayments } from 'src/app/shared/services/common.service';
import { PaymentAction } from 'src/app/shared/models/order';
import { Language } from 'src/app/shared/enum/language.enum';
import { Component, OnInit, inject } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { paiements } from 'src/app/shared/mocks/mocks';
import { Shipping } from 'src/app/shared/models/shipping';
import { UserCategory } from 'src/app/shared/enum/user-category.enum';
import { OrderSupplier } from 'src/app/shared/models/order-particular';
import { User } from 'src/app/shared/models/user.models';
import { Company } from 'src/app/shared/models/company.model';
import { OrderParticularService } from '../../services/order-particular.service';

@Component({
  selector: 'app-third-step',
  templateUrl: './third-step.component.html',
  styleUrls: ['./third-step.component.scss'],
})
export class ThirdStepComponent implements OnInit {

  cart: Cart;
  itemsLimited: CartItem[] = [];
  orderPrice: OrderPrice;
  isValidate: boolean = false;
  modePayment: string = '';
  isLoading = true;
  isFactory: boolean;
  isDiscount: boolean;
  PaymentMode: string;
  isCodePromoActive: Boolean;
  promoCodeAction = PromoCodeAction;
  shippingInfo: ShippingInfo;
  shipping: Partial<Shipping>;
  clientId: string | null = null;
  user: User;
  supplier: Company;

  // Méthode pour initialiser clientId, à appeler quand un commercial sélectionne un client
  selectClient(id: string) {
    this.clientId = id;
  }

  slideOpts = {
    initialSlide: 0,
    speed: 400,
    slidesPerView: 4,
    spaceBetween: 16,
  };

  slideProductOpts = {
    initialSlide: 0,
    speed: 400,
    slidesPerView: 4,
    spaceBetween: 10,
  };

  products: any[] = [1, 2, 3, 4];

  meansOfPayments: any[] = [];

  private orderSrv = inject(OrderService);
  private orderParticularSrv = inject(OrderParticularService);
  storageService = inject(StorageService);
  public productSrv = inject(ProductService);
  protected commonSrv = inject(CommonService);
  translateService = inject(TranslateConfigService);

  isFrench: boolean = this.translateService.currentLang === Language.French;

  constructor(
  ) {
    this.storageService.getUserConnected();
  }

  ngOnInit(): void { this.isLoading = true; }

  async ionViewWillEnter(): Promise<void> {
    this.isLoading = true;
    this.cart = JSON.parse(this.storageService.load('cart'));
    this.supplier = JSON.parse(this.storageService.load('supplier'));
    console.log(this.supplier);


    // this.checkPaymentMethod();
    delete this.cart.amount;
    this.cart.user = this.commonSrv?.user;

    // this.cart = await this.computerPriceSrv.getDetailOfCart(this.cart);
    this.itemsLimited = this.cart?.items?.slice(0, 3);
    // this.orderPrice = this.cart.amount;
    // this.shippingInfo = this.cart?.amount?.shippingInfo;
    // this.shipping = this.cart?.shipping;
    this.storageService.store('cart', JSON.stringify(this.cart));
    this.isLoading = false;
    this.PaymentMode = this.commonSrv?.user?.category == UserCategory.Particular ? 'credit_pay' : 'account_pay';

  }

  checkPaymentMethod(): void {
    this.isFactory = (this.cart.shipping['retrievePoint']
      && this.cart?.shipping?.['retrievePoint']?.name)?.toUpperCase()
      == this.cart?.store?.label?.toUpperCase();
    this.meansOfPayments = meansOfPayments.filter(means => {
      if ((means.label === PaymentAction.CREDIT && this.cart?.renderType !== 1)
        || (means.label === PaymentAction.CREDIT && this.cart?.renderType === 1 && !this.isFactory))
        return false;

      return this.commonSrv.user.authorizations.includes(means.label);
    });
  }

  async selectPaymentMode(payment: string): Promise<void> {
    this.isValidate = false;

    this.modePayment = payment;
    if (this.meansOfPayments[0].label === PaymentAction.MY_ACCOUNT) {
      this.meansOfPayments[0].image =
        this.meansOfPayments[0].label === PaymentAction.MY_ACCOUNT
          ? '/assets/logos/cimencam.svg'
          : '/assets/images/cimencam.png';
    }
    const modal = await this.commonSrv.modalCtrl.create({
      component:
        payment === PaymentAction.CREDIT
          ? PaiementOnCreditComponent
          : PaiementValidationComponent,
      initialBreakpoint: payment === PaymentAction.CREDIT ?
        0.75 : payment === PaymentAction.VISA ? 1 : 0.7,
      cssClass: 'modal',
      breakpoints: [0, 0.8, 0.85, 1],
      mode: 'ios',
      componentProps: {
        title:
          this.isFrench
            ? paiements[payment]?.titleForFrench
            : paiements[payment]?.titleForEnglish,
        type: payment,
        cart: this.cart,
        amount: this.orderPrice?.TTC,
      },
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
  }

  async doPaiement(): Promise<void> {
    this.isLoading = true;

    this.user = JSON.parse(this.storageService.load('USER_INFO'));
    delete this.cart.company;
    delete this.cart.user;
    let order: OrderSupplier = {
      user: this.user,
      supplier: this.supplier,
      cart: {
        ...this.cart,
      },
      qrCodeData: []
    };

    this.orderSrv.response = await this.orderParticularSrv.create(order);

    this.isLoading = false;
    if (!(this.orderSrv.response instanceof HttpErrorResponse)) {
      this.commonSrv.router.navigate(['order/new/four-step']);
    } else {
      this.commonSrv.showToast({
        color: 'danger',
        message: this.orderSrv.response.message
      });
    }
  }

}
